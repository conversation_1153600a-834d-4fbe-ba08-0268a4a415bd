// SVG图标库 - 解决CDN加载问题
const SVGIcons = {
    'shield-alt': '<svg viewBox="0 0 512 512"><path d="M466.5 83.7l-192-80a48.15 48.15 0 0 0-36.9 0l-192 80C27.7 91.1 16 108.6 16 128c0 198.5 114.5 335.7 221.5 380.3 11.8 4.9 25.1 4.9 36.9 0C360.1 472.6 496 349.3 496 128c0-19.4-11.7-36.9-29.5-44.3z"/></svg>',
    'chart-bar': '<svg viewBox="0 0 512 512"><path d="M32 32C14.3 32 0 46.3 0 64v336c0 17.7 14.3 32 32 32h448c17.7 0 32-14.3 32-32s-14.3-32-32-32H64V64c0-17.7-14.3-32-32-32zm96 128c-17.7 0-32 14.3-32 32v192c0 17.7 14.3 32 32 32s32-14.3 32-32V192c0-17.7-14.3-32-32-32zm96-64c-17.7 0-32 14.3-32 32v256c0 17.7 14.3 32 32 32s32-14.3 32-32V128c0-17.7-14.3-32-32-32zm96 32c-17.7 0-32 14.3-32 32v224c0 17.7 14.3 32 32 32s32-14.3 32-32V160c0-17.7-14.3-32-32-32zm96-16c-17.7 0-32 14.3-32 32v240c0 17.7 14.3 32 32 32s32-14.3 32-32V144c0-17.7-14.3-32-32-32z"/></svg>',
    'folder': '<svg viewBox="0 0 512 512"><path d="M64 480H448c35.3 0 64-28.7 64-64V160c0-35.3-28.7-64-64-64H298.5c-17 0-33.3-6.7-45.3-18.7L226.7 50.7c-12-12-28.3-18.7-45.3-18.7H64C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64z"/></svg>',
    'mobile-alt': '<svg viewBox="0 0 384 512"><path d="M80 0C44.7 0 16 28.7 16 64V448c0 35.3 28.7 64 64 64H304c35.3 0 64-28.7 64-64V64c0-35.3-28.7-64-64-64H80zM96 64H288c8.8 0 16 7.2 16 16s-7.2 16-16 16H96c-8.8 0-16-7.2-16-16s7.2-16 16-16zM192 448c-17.7 0-32-14.3-32-32s14.3-32 32-32s32 14.3 32 32s-14.3 32-32 32z"/></svg>',
    'key': '<svg viewBox="0 0 512 512"><path d="M336 352c97.2 0 176-78.8 176-176S433.2 0 336 0S160 78.8 160 176c0 18.7 2.9 36.8 8.3 53.7L7 391c-4.5 4.5-7 10.6-7 17v80c0 13.3 10.7 24 24 24h80c13.3 0 24-10.7 24-24V448h40c13.3 0 24-10.7 24-24V384h40c6.4 0 12.5-2.5 17-7l33.3-33.3c16.9 5.4 35 8.3 53.7 8.3zM376 96c22.1 0 40 17.9 40 40s-17.9 40-40 40s-40-17.9-40-40s17.9-40 40-40z"/></svg>',
    'search': '<svg viewBox="0 0 512 512"><path d="M416 208c0 45.9-14.9 88.3-40 122.7L502.6 457.4c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L330.7 376c-34.4 25.2-76.8 40-122.7 40C93.1 416 0 322.9 0 208S93.1 0 208 0S416 93.1 416 208zM208 352c79.5 0 144-64.5 144-144s-64.5-144-144-144S64 128.5 64 208s64.5 144 144 144z"/></svg>',
    'cog': '<svg viewBox="0 0 512 512"><path d="M495.9 166.6c3.2 8.7 .5 18.4-6.4 24.6l-43.3 39.4c1.1 8.3 1.7 16.8 1.7 25.4s-.6 17.1-1.7 25.4l43.3 39.4c6.9 6.2 9.6 15.9 6.4 24.6c-4.4 11.9-9.7 23.3-15.8 34.3l-4.7 8.1c-6.6 11-14 21.4-22.1 31.2c-5.9 7.2-15.7 9.6-24.5 6.8l-55.7-17.7c-13.4 10.3-28.2 18.9-44 25.4l-12.5 57.1c-2 9.1-9 16.3-18.2 17.8c-13.8 2.3-28 3.5-42.5 3.5s-28.7-1.2-42.5-3.5c-9.2-1.5-16.2-8.7-18.2-17.8l-12.5-57.1c-15.8-6.5-30.6-15.1-44-25.4L83.1 425.9c-8.8 2.8-18.6 .3-24.5-6.8c-8.1-9.8-15.5-20.2-22.1-31.2l-4.7-8.1c-6.1-11-11.4-22.4-15.8-34.3c-3.2-8.7-.5-18.4 6.4-24.6l43.3-39.4C64.6 273.1 64 264.6 64 256s.6-17.1 1.7-25.4L22.4 191.2c-6.9-6.2-9.6-15.9-6.4-24.6c4.4-11.9 9.7-23.3 15.8-34.3l4.7-8.1c6.6-11 14-21.4 22.1-31.2c5.9-7.2 15.7-9.6 24.5-6.8l55.7 17.7c13.4-10.3 28.2-18.9 44-25.4l12.5-57.1c2-9.1 9-16.3 18.2-17.8C227.3 1.2 241.5 0 256 0s28.7 1.2 42.5 3.5c9.2 1.5 16.2 8.7 18.2 17.8l12.5 57.1c15.8 6.5 30.6 15.1 44 25.4l55.7-17.7c8.8-2.8 18.6-.3 24.5 6.8c8.1 9.8 15.5 20.2 22.1 31.2l4.7 8.1c6.1 11 11.4 22.4 15.8 34.3zM256 336c44.2 0 80-35.8 80-80s-35.8-80-80-80s-80 35.8-80 80s35.8 80 80 80z"/></svg>',
    'plus': '<svg viewBox="0 0 448 512"><path d="M256 80c0-17.7-14.3-32-32-32s-32 14.3-32 32V224H48c-17.7 0-32 14.3-32 32s14.3 32 32 32H192V432c0 17.7 14.3 32 32 32s32-14.3 32-32V288H400c17.7 0 32-14.3 32-32s-14.3-32-32-32H256V80z"/></svg>',
    'info-circle': '<svg viewBox="0 0 512 512"><path d="M256 512c141.4 0 256-114.6 256-256S397.4 0 256 0S0 114.6 0 256S114.6 512 256 512zM216 336h24V272H216c-13.3 0-24-10.7-24-24s10.7-24 24-24h48c13.3 0 24 10.7 24 24v88h8c13.3 0 24 10.7 24 24s-10.7 24-24 24H216c-13.3 0-24-10.7-24-24s10.7-24 24-24zm40-144c-17.7 0-32-14.3-32-32s14.3-32 32-32s32 14.3 32 32s-14.3 32-32 32z"/></svg>',
    'exclamation-triangle': '<svg viewBox="0 0 512 512"><path d="M256 32c14.2 0 27.3 7.5 34.5 19.8l216 368c7.3 12.4 7.3 27.7 .2 40.1S486.3 480 472 480H40c-14.3 0-27.6-7.7-34.7-20.1s-7-27.8 .2-40.1l216-368C228.7 39.5 241.8 32 256 32zm0 128c-13.3 0-24 10.7-24 24V296c0 13.3 10.7 24 24 24s24-10.7 24-24V184c0-13.3-10.7-24-24-24zm32 224c0 17.7-14.3 32-32 32s-32-14.3-32-32s14.3-32 32-32s32 14.3 32 32z"/></svg>',
    'sync-alt': '<svg viewBox="0 0 512 512"><path d="M142.9 142.9c62.2-62.2 162.7-62.5 225.3-1L327 183c-6.9 6.9-8.9 17.2-5.2 26.2s12.5 14.8 22.2 14.8H463.5c0 0 0 0 0 0H472c13.3 0 24-10.7 24-24V72c0-9.7-5.8-18.5-14.8-22.2s-19.3-1.7-26.2 5.2L413.4 96.6c-87.6-86.5-228.7-86.2-315.8 1C73.2 122 55.6 150.7 44.8 181.4c-5.9 16.7 2.9 34.9 19.5 40.8s34.9-2.9 40.8-19.5c7.7-21.8 20.2-42.3 37.8-59.8zM16 312v7.6 .7V440c0 9.7 5.8 18.5 14.8 22.2s19.3 1.7 26.2-5.2L98.6 415.4c87.6 86.5 228.7 86.2 315.8-1C438.8 390 456.4 361.3 467.2 330.6c5.9-16.7-2.9-34.9-19.5-40.8s-34.9 2.9-40.8 19.5c-7.7 21.8-20.2 42.3-37.8 59.8c-62.2 62.2-162.7 62.5-225.3 1L185 329c6.9-6.9 8.9-17.2 5.2-26.2s-12.5-14.8-22.2-14.8H48.4h-.7H40c-13.3 0-24 10.7-24 24z"/></svg>',
    'copy': '<svg viewBox="0 0 512 512"><path d="M224 0c-35.3 0-64 28.7-64 64v224c0 35.3 28.7 64 64 64H448c35.3 0 64-28.7 64-64V64c0-35.3-28.7-64-64-64H224zM64 160c-35.3 0-64 28.7-64 64V448c0 35.3 28.7 64 64 64H288c35.3 0 64-28.7 64-64V384H288v64H64V224h64V160H64z"/></svg>',
    'check-circle': '<svg viewBox="0 0 512 512"><path d="M256 512c141.4 0 256-114.6 256-256S397.4 0 256 0S0 114.6 0 256S114.6 512 256 512zM369 209L241 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L335 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"/></svg>',
    'clock': '<svg viewBox="0 0 512 512"><path d="M256 0C114.6 0 0 114.6 0 256s114.6 256 256 256s256-114.6 256-256S397.4 0 256 0zM232 120V256c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2V120c0-13.3-10.7-24-24-24s-24 10.7-24 24z"/></svg>',
    'spinner': '<svg viewBox="0 0 512 512"><path d="M304 48c0 26.51-21.49 48-48 48s-48-21.49-48-48s21.49-48 48-48s48 21.49 48 48zm-48 368c-26.51 0-48 21.49-48 48s21.49 48 48 48s48-21.49 48-48s-21.49-48-48-48zm208-208c-26.51 0-48 21.49-48 48s21.49 48 48 48s48-21.49 48-48s-21.49-48-48-48zM96 256c0-26.51-21.49-48-48-48S0 229.49 0 256s21.49 48 48 48s48-21.49 48-48zm12.922 99.078c-26.51 0-48 21.49-48 48s21.49 48 48 48s48-21.49 48-48c0-26.509-21.491-48-48-48zm294.156 0c-26.51 0-48 21.49-48 48s21.49 48 48 48s48-21.49 48-48s-21.49-48-48-48zM108.922 60.922c-26.51 0-48 21.49-48 48s21.49 48 48 48s48-21.49 48-48s-21.49-48-48-48z"/></svg>',
    'list': '<svg viewBox="0 0 512 512"><path d="M40 48C26.7 48 16 58.7 16 72v48c0 13.3 10.7 24 24 24H88c13.3 0 24-10.7 24-24V72c0-13.3-10.7-24-24-24H40zM192 64c-17.7 0-32 14.3-32 32s14.3 32 32 32H480c17.7 0 32-14.3 32-32s-14.3-32-32-32H192zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32H480c17.7 0 32-14.3 32-32s-14.3-32-32-32H192zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32H480c17.7 0 32-14.3 32-32s-14.3-32-32-32H192zM16 232v48c0 13.3 10.7 24 24 24H88c13.3 0 24-10.7 24-24V232c0-13.3-10.7-24-24-24H40c-13.3 0-24 10.7-24 24zM40 368c-13.3 0-24 10.7-24 24v48c0 13.3 10.7 24 24 24H88c13.3 0 24-10.7 24-24V392c0-13.3-10.7-24-24-24H40z"/></svg>',
    'edit': '<svg viewBox="0 0 512 512"><path d="M471.6 21.7c-21.9-21.9-57.3-21.9-79.2 0L362.3 51.7l97.9 97.9 30.1-30.1c21.9-21.9 21.9-57.3 0-79.2L471.6 21.7zm-299.2 220c-6.1 6.1-10.8 13.6-13.5 21.9l-29.6 88.8c-2.9 8.6-.6 18.1 5.8 24.6s15.9 8.7 24.6 5.8l88.8-29.6c8.2-2.7 15.7-7.4 21.9-13.5L437.7 172.3 339.7 74.3 172.4 241.7zM96 64C43 64 0 107 0 160V416c0 53 43 96 96 96H352c53 0 96-43 96-96V320c0-17.7-14.3-32-32-32s-32 14.3-32 32v96c0 17.7-14.3 32-32 32H96c-17.7 0-32-14.3-32-32V160c0-17.7 14.3-32 32-32h96c17.7 0 32-14.3 32-32s-14.3-32-32-32H96z"/></svg>',
    'play': '<svg viewBox="0 0 384 512"><path d="M73 39c-14.8-9.1-33.4-9.4-48.5-.9S0 62.6 0 80V432c0 17.4 9.4 33.4 24.5 41.9s33.7 8.1 48.5-.9L361 297c14.3-8.7 23-24.2 23-41s-8.7-32.2-23-41L73 39z"/></svg>',
    'pause': '<svg viewBox="0 0 320 512"><path d="M48 64C21.5 64 0 85.5 0 112V400c0 26.5 21.5 48 48 48H80c26.5 0 48-21.5 48-48V112c0-26.5-21.5-48-48-48H48zm192 0c-26.5 0-48 21.5-48 48V400c0 26.5 21.5 48 48 48h32c26.5 0 48-21.5 48-48V112c0-26.5-21.5-48-48-48H240z"/></svg>',
    'trash': '<svg viewBox="0 0 448 512"><path d="M135.2 17.7L128 32H32C14.3 32 0 46.3 0 64S14.3 96 32 96H416c17.7 0 32-14.3 32-32s-14.3-32-32-32H320l-7.2-14.3C307.4 6.8 296.3 0 284.2 0H163.8c-12.1 0-23.2 6.8-28.6 17.7zM416 128H32L53.2 467c1.6 25.3 22.6 45 47.9 45H346.9c25.3 0 46.3-19.7 47.9-45L416 128z"/></svg>',
    'chevron-left': '<svg viewBox="0 0 320 512"><path d="M9.4 233.4c-12.5 12.5-12.5 32.8 0 45.3l192 192c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L77.3 256 246.6 86.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0l-192 192z"/></svg>',
    'chevron-right': '<svg viewBox="0 0 320 512"><path d="m310.6 233.4c12.5 12.5 12.5 32.8 0 45.3l-192 192c-12.5 12.5-32.8 12.5-45.3 0s-12.5-32.8 0-45.3L242.7 256 73.4 86.6c-12.5-12.5-12.5-32.8 0-45.3s32.8-12.5 45.3 0l192 192z"/></svg>',
    'calendar-day': '<svg viewBox="0 0 448 512"><path d="M128 0c17.7 0 32 14.3 32 32V64H288V32c0-17.7 14.3-32 32-32s32 14.3 32 32V64h48c26.5 0 48 21.5 48 48v48H0V112C0 85.5 21.5 64 48 64H96V32c0-17.7 14.3-32 32-32zM0 192H448V464c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V192zm80 64c-8.8 0-16 7.2-16 16v64c0 8.8 7.2 16 16 16H368c8.8 0 16-7.2 16-16V272c0-8.8-7.2-16-16-16H80z"/></svg>'
};

// 替换图标为SVG
function replaceIconsWithSVG() {
    // 查找所有使用fas fa-*类的元素，但排除已经替换过的
    const icons = document.querySelectorAll('[class*="fas fa-"], [class*="fa fa-"]:not([data-svg-replaced])');

    let replacedCount = 0;
    icons.forEach(icon => {
        // 双重检查：如果已经标记为替换过或包含SVG，跳过
        if (icon.hasAttribute('data-svg-replaced') || icon.querySelector('svg')) {
            return;
        }

        const classes = icon.className.split(' ');
        let iconName = '';

        // 查找图标名称
        classes.forEach(cls => {
            if (cls.startsWith('fa-') && cls !== 'fa' && cls !== 'fas' && cls !== 'far' && cls !== 'fab') {
                iconName = cls.substring(3); // 移除 'fa-' 前缀
            }
        });

        // 如果找到对应的SVG图标，替换它
        if (iconName && SVGIcons[iconName]) {
            icon.innerHTML = SVGIcons[iconName];
            icon.style.setProperty('width', '1em', 'important');
            icon.style.setProperty('height', '1em', 'important');
            icon.style.setProperty('display', 'inline-block', 'important');
            icon.style.setProperty('fill', 'currentColor', 'important');
            icon.style.setProperty('vertical-align', '-0.125em', 'important');  // 修复垂直对齐
            icon.style.setProperty('line-height', '1', 'important');

            // 处理旋转动画
            if (classes.includes('fa-spin')) {
                icon.style.animation = 'fa-spin 2s infinite linear';
            }

            // 标记为已替换
            icon.setAttribute('data-svg-replaced', 'true');
            replacedCount++;
        }
    });

    if (replacedCount > 0) {
        console.log(`[Icons Debug] Replaced ${replacedCount} icons with SVG`);
    }
}

// 添加CSS动画和对齐样式
const style = document.createElement('style');
style.textContent = `
    @keyframes fa-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    .fas, .fa {
        display: inline-block !important;
        width: 1em !important;
        height: 1em !important;
        fill: currentColor !important;
        vertical-align: -0.125em !important;
        line-height: 1 !important;
    }
    .fas svg, .fa svg {
        width: 100% !important;
        height: 100% !important;
        vertical-align: top !important;
    }
    /* 确保在不同容器中的对齐 - 使用更高优先级 */
    .sidebar-item .fas,
    .sidebar-item .fa,
    .stat-card-icon .fas,
    .stat-card-icon .fa,
    .btn .fas,
    .btn .fa {
        vertical-align: -0.125em !important;
        line-height: 1 !important;
        display: inline-block !important;
    }
    /* 特别针对侧边栏图标的对齐修复 */
    .sidebar-item i[data-svg-replaced="true"] {
        vertical-align: -0.125em !important;
        line-height: 1 !important;
        display: inline-block !important;
    }
`;
document.head.appendChild(style);

// 智能图标加载检测
function checkIconsLoaded() {
    return new Promise((resolve) => {
        // 检查是否有Font Awesome图标正常显示
        const testIcon = document.createElement('i');
        testIcon.className = 'fas fa-home';  // 使用一个常见的图标
        testIcon.style.position = 'absolute';
        testIcon.style.left = '-9999px';
        testIcon.style.fontSize = '16px';
        document.body.appendChild(testIcon);

        // 等待一下让样式生效
        setTimeout(() => {
            const computed = window.getComputedStyle(testIcon, ':before');
            const hasContent = computed.content && computed.content !== 'none' && computed.content !== '""';

            // 检查字体族是否包含Font Awesome
            const fontFamily = computed.fontFamily;
            const hasFontAwesome = fontFamily && fontFamily.includes('Font Awesome');

            document.body.removeChild(testIcon);

            console.log('[Icons Debug] Content check:', hasContent, 'Font family:', fontFamily);

            // 如果Font Awesome字体可用，就不需要SVG替换
            if (hasFontAwesome || hasContent) {
                console.log('[Icons Debug] Font Awesome is working, no SVG replacement needed');
                resolve(true);
            } else {
                console.log('[Icons Debug] Font Awesome not working, using SVG replacement');
                resolve(false);
            }
        }, 200);
    });
}

// 检查CDN链接是否可用
function checkCDNAvailable() {
    return new Promise((resolve) => {
        const link = document.getElementById('cdn-fa');
        if (!link) {
            console.log('[Icons Debug] No CDN link found');
            resolve(false);
            return;
        }

        // 检查CSS是否已加载
        const sheets = document.styleSheets;
        let cdnLoaded = false;

        try {
            for (let sheet of sheets) {
                if (sheet.href && sheet.href.includes('font-awesome')) {
                    cdnLoaded = true;
                    break;
                }
            }
        } catch (e) {
            console.log('[Icons Debug] Error checking stylesheets:', e);
        }

        console.log('[Icons Debug] CDN stylesheet loaded:', cdnLoaded);
        resolve(cdnLoaded);
    });
}

// 页面加载完成后智能选择图标系统
document.addEventListener('DOMContentLoaded', function() {
    console.log('[Icons Debug] Starting intelligent icon loading system...');

    // 等待CDN有足够时间加载
    setTimeout(async () => {
        const cdnAvailable = await checkCDNAvailable();
        const fontAwesomeWorking = await checkIconsLoaded();

        if (cdnAvailable && fontAwesomeWorking) {
            console.log('✅ [Icons] Font Awesome CDN is working properly, using CDN icons');
            console.log('📊 [Icons] Icon System: Font Awesome CDN (Recommended)');
            // CDN工作正常，不需要SVG替换，确保SVG模式被禁用
            disableSVGMode();
        } else {
            console.log('⚠️ [Icons] Font Awesome CDN failed or not working, falling back to SVG icons');
            console.log('📊 [Icons] Icon System: Custom SVG (Fallback)');
            enableSVGMode();
            replaceIconsWithSVG();
        }
    }, 500); // 给CDN更多时间加载
});

// 为动态添加的内容提供支持 - 只在SVG模式下启用
let isReplacing = false;
let svgModeEnabled = false;
let observer = null;

function enableSVGMode() {
    svgModeEnabled = true;

    // 启动MutationObserver监听动态内容
    if (!observer) {
        observer = new MutationObserver(function(mutations) {
            if (isReplacing || !svgModeEnabled) return; // 防止无限循环

            let shouldReplace = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // 只有当添加了包含未替换图标的节点时才替换
                    for (let node of mutation.addedNodes) {
                        if (node.nodeType === 1) { // 元素节点
                            if (node.querySelector && node.querySelector('[class*="fas fa-"], [class*="fa fa-"]:not([data-svg-replaced])')) {
                                console.log('[Icons Debug] New icons detected in DOM, triggering replacement...');
                                shouldReplace = true;
                                break;
                            }
                        }
                    }
                }
            });

            if (shouldReplace) {
                isReplacing = true;
                setTimeout(() => {
                    replaceIconsWithSVG();
                    isReplacing = false;
                }, 10);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
}

function disableSVGMode() {
    svgModeEnabled = false;
    if (observer) {
        observer.disconnect();
        observer = null;
    }
}
