<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#1976d2">
    <title>{{ project_name or '短信接收器' }}</title>
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/user.css?v={{ timestamp or '1' }}">
</head>
<body>
    <div class="app-container">
        <!-- 头部区域 -->
        <div class="header">
            <h1 class="header-title">{{ project_name or '短信接收器' }}</h1>
            {% if not key %}
            <p class="header-subtitle">请输入您的卡密验证身份</p>
            {% endif %}
        </div>

        {% if not key %}
        <!-- 访问提示区域 -->
        <div class="main-card">
            <div class="access-notice">
                <div class="notice-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="notice-content">
                    <h3 class="notice-title">请使用专用链接访问</h3>
                    <p class="notice-text">
                        本系统需要通过包含卡密的专用链接进行访问。<br>
                        请使用您获得的完整链接地址，或联系管理员获取正确的访问链接。
                    </p>
                    <div class="notice-example">
                        <strong>链接格式示例：</strong><br>
                        <code>http://域名/?key=您的卡密</code>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <!-- 取号服务区域 -->
        <div class="main-card">
            <input type="hidden" id="key-value" value="{{ key }}">

            {% if error %}
            <!-- 错误状态 -->
            <div class="status-message error" style="display: block;">
                {{ error }}
            </div>
            {% endif %}

                {% if show_get_number %}
                <!-- 获取号码状态 -->
                <div class="action-section">
                    <button class="btn btn-primary btn-large" onclick="getNumberFromServer()">
                        <i class="bi bi-phone-fill"></i>
                        获取号码
                    </button>

                    <!-- 通知容器 -->
                    <div id="notification-container" class="notification-container"></div>
                </div>
                {% endif %}

                {% if number %}
                <!-- 已分配号码状态 -->
                <div class="number-section">
                    <div class="replace-button-container">
                        <button id="replace-btn" class="replace-btn-center" onclick="replaceNumber()" disabled>
                            <i class="bi bi-arrow-clockwise"></i>
                            更换号码 (<span id="countdown">{{ ui_config.REPLACE_NUMBER_COOLDOWN }}</span>s)
                        </button>
                    </div>

                    <div class="form-field">
                        <label class="form-label">已分配号码:</label>
                        <div class="number-display">
                            <input type="text" class="number-value" value="{{ number }}" readonly>
                            <button class="copy-btn" onclick="copyToClipboard('{{ number }}')">
                                <i class="bi bi-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>

                {% if not sms_code %}
                <div class="waiting-section">
                    <div class="form-field">
                        <label class="form-label">短信内容:</label>
                        <div class="form-input-group">
                            <textarea class="form-textarea auto-height" readonly placeholder="正在监听短信，请耐心等待... 剩余时间: {{ ui_config.SMS_WAIT_COUNTDOWN }}秒" id="waiting-textarea"></textarea>
                            <button class="form-copy-btn" disabled>
                                <i class="bi bi-copy"></i>
                            </button>
                        </div>
                        <div class="sms-message-center">
                            <p>{{ ui_config.WAITING_SMS_MESSAGE }}</p>
                        </div>
                    </div>

                    <!-- 通知容器 -->
                    <div id="notification-container" class="notification-container"></div>
                </div>

                <script>
                    // 存储用户号码ID
                    {% if user_number_id %}
                    localStorage.setItem('user_number_id', '{{ user_number_id }}');
                    {% endif %}

                    // 短信等待倒计时
                    let smsCountdown = {{ ui_config.SMS_WAIT_COUNTDOWN }};
                    const waitingTextarea = document.getElementById('waiting-textarea');

                    const smsCountdownInterval = setInterval(() => {
                        smsCountdown--;
                        if (waitingTextarea) {
                            waitingTextarea.placeholder = `正在监听短信，请耐心等待... 剩余时间: ${smsCountdown}秒`;
                        }
                        if (smsCountdown <= 0) {
                            clearInterval(smsCountdownInterval);
                            if (waitingTextarea) {
                                waitingTextarea.placeholder = '等待超时，请刷新页面重试';
                            }
                        }
                    }, 1000);
                </script>
                {% endif %}
                {% endif %}

                {% if sms_code %}
                <div class="success-section">
                    <div class="success-header">
                        <div class="success-icon">
                            <i class="bi bi-check-circle-fill"></i>
                        </div>
                        <h3>验证码获取成功</h3>
                        <p>已成功接收到验证码</p>
                    </div>

                    <div class="form-field">
                        <label class="form-label">验证码:</label>
                        <div class="form-input-group">
                            <input type="text" class="form-input code-input" value="{{ sms_code }}" readonly>
                            <button class="form-copy-btn" onclick="copyToClipboard('{{ sms_code }}')">
                                <i class="bi bi-copy"></i>
                            </button>
                        </div>
                    </div>

                    {% if sms_content %}
                    <div class="form-field">
                        <label class="form-label">短信内容:</label>
                        <div class="form-input-group">
                            <textarea class="form-textarea" readonly>{{ sms_content }}</textarea>
                            <button class="form-copy-btn" onclick="copyToClipboard('{{ sms_content }}')">
                                <i class="bi bi-copy"></i>
                            </button>
                        </div>
                    </div>
                    {% endif %}

                    <!-- 成功提示条 -->
                    <div class="success-banner">
                        等到验证成功
                    </div>

                    {% if use_time %}
                    <div class="time-info">
                        <i class="bi bi-clock"></i>
                        <span>接收时间: {{ use_time }}</span>
                    </div>
                    {% endif %}

                    <!-- 通知容器 -->
                    <div id="notification-container" class="notification-container"></div>
                </div>
                {% endif %}
                {% endif %}
            </div>
        </div>

        {% if project_description %}
        <!-- 项目描述 -->
        <div class="project-info-outside">
            <div class="info-content">
                {{ project_description|safe }}
            </div>
        </div>
        {% endif %}
    </div>

    <!-- 脚本文件 -->
    <script src="/static/js/utils.js?v={{ timestamp or '1' }}"></script>
    <script>
        // 将UI配置传递给JavaScript
        {% if ui_config %}
        window.UI_CONFIG = {{ ui_config | tojson }};
        {% endif %}
    </script>
    <script src="/static/js/user.js?v={{ timestamp or '1' }}"></script>
</body>
</html>
