# ======================= 服务器配置 =======================
SERVER_CONFIG = {
    'HOST': '0.0.0.0',  # 监听地址，0.0.0.0表示监听所有网卡，127.0.0.1表示只监听本地
    'PORT': 5600,       # 监听端口，可修改为其他端口如8080、3000等
    'DEBUG': True,      # 是否开启调试模式，生产环境建议设为False
    'THREADED': True,   # 是否开启多线程，建议保持True
}

# ======================= 用户界面配置 =======================
UI_CONFIG = {
    # 更换号码冷却时间（秒）- 用户更换号码后需要等待的时间
    'REPLACE_NUMBER_COOLDOWN': 60,

    # 等待验证码时的提示文本
    'WAITING_SMS_MESSAGE': '请复制号码到 网页/客户端 使用，然后在此等待验证码',

    # 状态提示信息显示时长（秒）
    'STATUS_DISPLAY_TIME': 10,

    # 消息提示显示时间（毫秒）- 控制复制成功等消息的显示时长
    'NOTIFICATION_DISPLAY_TIME': 3000,

    # 是否显示等待日志按钮
    'SHOW_WAIT_LOG_BUTTON': False,
}

# ======================= 验证码获取配置 =======================

# 轮询配置
MAX_POLLING_ATTEMPTS = 80  # 最大轮询次数（网络错误时也会继续轮询）
POLL_INTERVAL_MS = 3000    # 轮询间隔时间（毫秒）
# 注意：倒计时时间 = MAX_POLLING_ATTEMPTS * POLL_INTERVAL_MS / 1000 秒

# 短信API主机地址
SMS_API_HOST = 'http://sms.newszfang.vip:3000'

# 获取验证码时是否打印调试日志
ENABLE_SMS_DEBUG_LOG = True  # 设为True开启调试日志，False关闭

# 验证码正则表达式（按优先级排序）
CODE_REGEX_PATTERNS = [
    r'验证码[：:]\s*(\d{4,8})',
    r'验证码[是为]\s*(\d{4,8})',
    r'code[：:]\s*(\d{4,8})',
    r'(\d{4,8})',
]

# ======================= 安全配置 =======================
# 请求频率限制
RATE_LIMIT_MAX_REQUESTS = 5   # 最大请求次数
RATE_LIMIT_WINDOW_MINUTES = 5 # 时间窗口（分钟）

# IP请求限制（防止大量请求导致网站卡死）
IP_RATE_LIMIT_MAX_REQUESTS = 30  # IP每分钟最大请求次数
IP_RATE_LIMIT_WINDOW_MINUTES = 1 # IP限制时间窗口（分钟）

# 卡密使用限制
CARD_HOURLY_LIMIT = 3  # 每个卡密1小时内可以获取手机号码的次数（包括更换号码）

# 管理员账户配置
ADMIN_DEFAULT_PASSWORD = 'admin123'  # 初始管理员密码

# ======================= 号码状态管理配置 =======================
# 卡密与号码绑定超时时间（分钟）- 无操作后自动解除绑定并禁用号码
CARD_NUMBER_BINDING_TIMEOUT = 5

# ======================= 数据库配置 =======================
# 已用卡密保留天数（0表示永久保留，建议设置为30-90天）
CARD_RETENTION_DAYS = 30


