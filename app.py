from flask import Flask, request, jsonify, render_template, redirect, url_for, session, send_file
from flask_cors import CORS
import requests
import re
import markdown
from datetime import datetime, timedelta, timezone, date
from models import db, Project, Group, Number, Card, UserNumber, Admin, DailyStats, CardUsageRecord, CardNumberBinding
from sqlalchemy import func, desc, and_
import os
import uuid
import hashlib
from functools import wraps
import random
import string
from threading import Thread
import time
from collections import defaultdict
import io
import json
from config import (
    UI_CONFIG, SERVER_CONFIG, CODE_REGEX_PATTERNS, SMS_API_HOST,
    RATE_LIMIT_MAX_REQUESTS, RATE_LIMIT_WINDOW_MINUTES, CARD_RETENTION_DAYS,
    ENABLE_SMS_DEBUG_LOG, IP_RATE_LIMIT_MAX_REQUESTS, IP_RATE_LIMIT_WINDOW_MINUTES,
    CARD_HOURLY_LIMIT, ADMIN_DEFAULT_PASSWORD, CARD_NUMBER_BINDING_TIMEOUT
)

# --- App Initialization ---
BASE_DIR = os.path.abspath(os.path.dirname(__file__))
INSTANCE_DIR = os.path.join(BASE_DIR, 'instance')
os.makedirs(INSTANCE_DIR, exist_ok=True)
DB_PATH = os.path.join(INSTANCE_DIR, 'sms.db')

app = Flask(__name__, static_folder='static', template_folder='templates')
app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{DB_PATH}'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=31)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', os.urandom(24).hex())
CORS(app)
db.init_app(app)

# 添加缓存控制中间件
@app.after_request
def add_cache_control_headers(response):
    """为静态文件添加缓存控制头"""
    if request.endpoint == 'static':
        # 记录静态文件访问日志（特别是字体和CSS文件）
        if request.path.endswith(('.css', '.woff', '.woff2', '.ttf', '.eot')):
            print(f"[STATIC] Serving: {request.path} - Status: {response.status_code}")
            if response.status_code != 200:
                print(f"[STATIC ERROR] Failed to serve: {request.path}")

        # 对于静态文件，在开发模式下禁用缓存
        if app.debug:
            response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
            response.headers['Pragma'] = 'no-cache'
            response.headers['Expires'] = '0'
        else:
            # 生产模式下允许短时间缓存
            response.headers['Cache-Control'] = 'public, max-age=300'  # 5分钟缓存
    elif request.endpoint in ['root', 'sms_token_entry', 'admin_dashboard']:
        # 对于HTML页面，禁用缓存
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'
    return response

# --- Rate Limiting ---
request_counts = defaultdict(list)

# --- Caching ---
cache = {}
CACHE_TIMEOUT = 60  # 缓存60秒

def get_cache_key(prefix, *args):
    """生成缓存键"""
    return f"{prefix}:{':'.join(map(str, args))}"

def get_cached_data(key):
    """获取缓存数据"""
    if key in cache:
        data, timestamp = cache[key]
        if time.time() - timestamp < CACHE_TIMEOUT:
            return data
        else:
            del cache[key]
    return None

def set_cached_data(key, data):
    """设置缓存数据"""
    cache[key] = (data, time.time())

def clear_cache_by_prefix(prefix):
    """清除指定前缀的缓存"""
    keys_to_delete = [key for key in cache.keys() if key.startswith(prefix)]
    for key in keys_to_delete:
        del cache[key]

def check_rate_limit(ip, max_requests=10, window_minutes=5):
    """简单的速率限制：每5分钟最多10次请求"""
    now = time.time()
    window_start = now - (window_minutes * 60)

    # 清理过期的请求记录
    request_counts[ip] = [req_time for req_time in request_counts[ip] if req_time > window_start]

    # 检查是否超过限制
    if len(request_counts[ip]) >= max_requests:
        return False

    # 记录当前请求
    request_counts[ip].append(now)
    return True

def check_ip_rate_limit(ip):
    """增强的IP请求限制：防止大量请求导致网站卡死"""
    return check_rate_limit(ip, IP_RATE_LIMIT_MAX_REQUESTS, IP_RATE_LIMIT_WINDOW_MINUTES)

def check_card_hourly_limit(card_code):
    """检查卡密1小时内使用次数限制"""
    try:
        one_hour_ago = get_beijing_time() - timedelta(hours=1)

        # 查询1小时内的使用记录
        usage_count = db.session.query(func.count(CardUsageRecord.id)).filter(
            CardUsageRecord.card_code == card_code,
            CardUsageRecord.created_at >= one_hour_ago
        ).scalar()

        return usage_count < CARD_HOURLY_LIMIT
    except Exception as e:
        print(f"检查卡密使用限制时出错: {e}")
        return True  # 出错时允许使用

def record_card_usage(card_code, action_type, ip_address):
    """记录卡密使用"""
    try:
        usage_record = CardUsageRecord(
            card_code=card_code,
            action_type=action_type,
            ip_address=ip_address
        )
        db.session.add(usage_record)
        db.session.commit()
    except Exception as e:
        print(f"记录卡密使用时出错: {e}")
        db.session.rollback()

# --- Helpers ---
def get_beijing_time():
    return datetime.now(timezone(timedelta(hours=8))).replace(tzinfo=None)

def generate_token():
    return hashlib.sha256(uuid.uuid4().bytes).hexdigest()

def clean_bom_characters(text):
    """清理字符串中的BOM字符和其他不可见字符"""
    if not text:
        return text
    # 移除BOM字符 (UTF-8 BOM: \ufeff, UTF-16 BOM: \ufffe, \ufeff)
    text = text.replace('\ufeff', '').replace('\ufffe', '')
    # 移除其他常见的不可见字符
    text = text.replace('\u200b', '').replace('\u200c', '').replace('\u200d', '')
    return text.strip()



def get_dynamic_ui_config():
    """获取动态UI配置"""
    # 直接使用配置文件中的设置
    from config import MAX_POLLING_ATTEMPTS, POLL_INTERVAL_MS

    # 根据轮询配置计算倒计时时间
    sms_wait_countdown = int(MAX_POLLING_ATTEMPTS * POLL_INTERVAL_MS / 1000)

    dynamic_config = {
        'REPLACE_NUMBER_COOLDOWN': UI_CONFIG.get('REPLACE_NUMBER_COOLDOWN', 60),
        'SMS_WAIT_COUNTDOWN': sms_wait_countdown,
        'MAX_POLLING_ATTEMPTS': MAX_POLLING_ATTEMPTS,
        'POLL_INTERVAL_MS': POLL_INTERVAL_MS,
        'WAITING_SMS_MESSAGE': UI_CONFIG.get('WAITING_SMS_MESSAGE', '请复制号码到 网页/客户端 使用，然后在此等待验证码\n正在持续获取验证码中，请耐心等待...'),
        'STATUS_DISPLAY_TIME': UI_CONFIG.get('STATUS_DISPLAY_TIME', 10),
        'SHOW_WAIT_LOG_BUTTON': UI_CONFIG.get('SHOW_WAIT_LOG_BUTTON', False),
    }
    return dynamic_config

def update_daily_stats(project_id, cards_used=0, cards_generated=0):
    """更新每日统计数据"""
    today = date.today()
    stats = db.session.execute(
        db.select(DailyStats).filter_by(project_id=project_id, date=today)
    ).scalars().first()

    if stats:
        stats.cards_used += cards_used
        stats.cards_generated += cards_generated
        stats.updated_at = get_beijing_time()
    else:
        stats = DailyStats(
            project_id=project_id,
            date=today,
            cards_used=cards_used,
            cards_generated=cards_generated
        )
        db.session.add(stats)

    db.session.commit()

    # 清理相关缓存
    clear_cache_by_prefix('dashboard_stats')
    clear_cache_by_prefix('projects_ranking')

# --- Decorators ---
def admin_login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'admin_logged_in' not in session:
            return redirect(url_for('admin_login'))
        return f(*args, **kwargs)
    return decorated_function

def require_token(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = request.headers.get('X-CSRF-Token')
        if not token or token != session.get('csrf_token'):
            return jsonify({'error': '无效的CSRF令牌'}), 403
        return f(*args, **kwargs)
    return decorated_function

# --- Admin Auth Routes ---
@app.route('/admin/login', methods=['GET', 'POST'])
def admin_login():
    if 'csrf_token' not in session:
        session['csrf_token'] = generate_token()
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        admin = db.session.execute(db.select(Admin).filter_by(username=username)).scalars().first()
        if admin and admin.check_password(password):
            session['admin_logged_in'] = True
            session.permanent = True
            return redirect(url_for('admin_dashboard'))
        return render_template('login.html', error='用户名或密码错误', csrf_token=session['csrf_token'])
    return render_template('login.html', csrf_token=session['csrf_token'])

@app.route('/admin/logout')
def admin_logout():
    session.clear()
    return redirect(url_for('admin_login'))

# --- Admin Page Routes ---
@app.route('/admin')
def admin_index():
    """后台根路由，根据登录状态重定向到登录页或后台"""
    if 'admin_logged_in' in session:
        return redirect(url_for('admin_dashboard'))
    return redirect(url_for('admin_login'))

@app.route('/admin/dashboard')
@admin_login_required
def admin_dashboard():
    """受保护的后台主页面"""
    if 'csrf_token' not in session:
        session['csrf_token'] = generate_token()
    import time
    timestamp = str(int(time.time()))  # 添加时间戳防止缓存
    return render_template('admin.html', csrf_token=session['csrf_token'], timestamp=timestamp)

# --- 新增：统计API ---
@app.route('/api/admin/dashboard/stats', methods=['GET'])
@admin_login_required
def dashboard_stats():
    """获取仪表板统计数据（带缓存）"""
    try:
        today = date.today()
        cache_key = get_cache_key('dashboard_stats', today.isoformat())

        # 尝试从缓存获取数据
        cached_data = get_cached_data(cache_key)
        if cached_data:
            return jsonify({
                'success': True,
                'stats': cached_data,
                'cached': True
            })

        # 基础统计
        total_used_cards = db.session.query(func.count(Card.id)).filter(Card.is_used == True).scalar()

        # 今日统计
        today_used = db.session.query(func.sum(DailyStats.cards_used)).filter(
            DailyStats.date == today
        ).scalar() or 0

        # 昨日统计
        yesterday = today - timedelta(days=1)
        yesterday_used = db.session.query(func.sum(DailyStats.cards_used)).filter(
            DailyStats.date == yesterday
        ).scalar() or 0

        today_generated = db.session.query(func.sum(DailyStats.cards_generated)).filter(
            DailyStats.date == today
        ).scalar() or 0

        # 最近7天使用趋势
        seven_days_ago = today - timedelta(days=6)
        daily_trend = db.session.execute(
            db.select(DailyStats.date, func.sum(DailyStats.cards_used).label('total_used'))
            .filter(DailyStats.date >= seven_days_ago)
            .group_by(DailyStats.date)
            .order_by(DailyStats.date)
        ).all()

        # 填充缺失的日期
        trend_data = []
        for i in range(7):
            current_date = seven_days_ago + timedelta(days=i)
            used_count = 0
            for trend in daily_trend:
                if trend.date == current_date:
                    used_count = trend.total_used or 0
                    break
            trend_data.append({
                'date': current_date.strftime('%m-%d'),
                'used': used_count
            })

        stats_data = {
            'total_used_cards': total_used_cards,
            'today_used': today_used,
            'yesterday_used': yesterday_used,
            'today_generated': today_generated,
            'daily_trend': trend_data
        }

        # 缓存结果
        set_cached_data(cache_key, stats_data)

        return jsonify({
            'success': True,
            'stats': stats_data,
            'cached': False
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/admin/projects/ranking', methods=['GET'])
@admin_login_required
def projects_ranking():
    """获取项目使用量排行（优化版本，带缓存）"""
    try:
        today = date.today()
        cache_key = get_cache_key('projects_ranking', today.isoformat())

        # 尝试从缓存获取数据
        cached_data = get_cached_data(cache_key)
        if cached_data:
            return jsonify({
                'success': True,
                'ranking': cached_data,
                'cached': True
            })

        # 使用单个优化的查询获取所有需要的数据
        ranking_query = db.session.execute(
            db.select(
                Project.id,
                Project.name,
                func.coalesce(func.count(Card.id).filter(Card.is_used == True), 0).label('used_cards')
            )
            .outerjoin(Card, Project.id == Card.project_id)
            .group_by(Project.id, Project.name)
            .order_by(desc('used_cards'))
        ).all()

        # 获取今日使用量
        today_stats = db.session.execute(
            db.select(DailyStats.project_id, func.sum(DailyStats.cards_used).label('today_used'))
            .filter(DailyStats.date == today)
            .group_by(DailyStats.project_id)
        ).all()

        today_dict = {stat.project_id: stat.today_used for stat in today_stats}

        # 构建结果
        ranking = []
        for stat in ranking_query:
            used_cards = stat.used_cards or 0
            today_used = today_dict.get(stat.id, 0)

            ranking.append({
                'id': stat.id,
                'name': stat.name,
                'today_used': today_used,
                'used_cards': used_cards
            })

        # 缓存结果
        set_cached_data(cache_key, ranking)

        return jsonify({
            'success': True,
            'ranking': ranking,
            'cached': False
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# --- 项目管理API ---
@app.route('/api/admin/projects', methods=['GET', 'POST'])
@admin_login_required
def admin_projects():
    if request.method == 'POST':
        data = request.json or {}
        name = data.get('name')
        sms_title = data.get('sms_title')
        if not name or not sms_title:
            return jsonify({'success': False, 'error': '项目名称和短信标题不能为空'}), 400

        # 处理验证码正则表达式
        code_regex_patterns = data.get('code_regex_patterns', '').strip()
        if code_regex_patterns:
            # 验证正则表达式格式，每行一个
            try:
                import re
                patterns = [p.strip() for p in code_regex_patterns.split('\n') if p.strip()]
                # 测试每个正则表达式是否有效
                for pattern in patterns:
                    re.compile(pattern)
                code_regex_patterns = '\n'.join(patterns)
            except re.error:
                return jsonify({'success': False, 'error': '验证码正则表达式格式无效'}), 400
        else:
            code_regex_patterns = None

        new_project = Project(
            name=name,
            sms_title=sms_title,
            description=data.get('description'),
            code_regex_patterns=code_regex_patterns
        )
        db.session.add(new_project)
        db.session.commit()
        return jsonify({'success': True, 'project': {'id': new_project.id, 'name': new_project.name}})

    # 获取项目及其卡密统计
    projects_with_stats = db.session.execute(
        db.select(
            Project.id,
            Project.name,
            Project.description,
            Project.is_random_global,
            Project.code_regex_patterns,
            Project.created_at,
            func.coalesce(func.count(Card.id), 0).label('total_cards'),
            func.coalesce(func.count(Card.id).filter(Card.is_used == False), 0).label('available_cards')
        )
        .outerjoin(Card, Project.id == Card.project_id)
        .group_by(Project.id, Project.name, Project.description, Project.is_random_global,
                  Project.code_regex_patterns, Project.created_at)
        .order_by(Project.id.desc())
    ).all()

    return jsonify([{
        'id': p.id,
        'name': p.name,
        'description': p.description,
        'is_random_global': p.is_random_global,
        'code_regex_patterns': p.code_regex_patterns,
        'created_at': p.created_at.isoformat() if p.created_at else None,
        'total_cards': p.total_cards,
        'available_cards': p.available_cards
    } for p in projects_with_stats])

@app.route('/api/admin/projects/<int:project_id>', methods=['GET', 'PUT', 'DELETE'])
@admin_login_required
def manage_project(project_id):
    project = db.session.get(Project, project_id)
    if not project:
        return jsonify({'error': '项目未找到'}), 404

    if request.method == 'GET':
        return jsonify({
            'id': project.id,
            'name': project.name,
            'sms_title': project.sms_title,
            'description': project.description,
            'is_random_global': project.is_random_global,
            'code_regex_patterns': project.code_regex_patterns,
            'created_at': project.created_at.isoformat() if project.created_at else None
        })

    elif request.method == 'PUT':
        # 检查CSRF token
        token = request.headers.get('X-CSRF-Token')
        if not token or token != session.get('csrf_token'):
            return jsonify({'error': '无效的CSRF令牌'}), 403

        data = request.json or {}
        project.name = data.get('name', project.name)
        project.sms_title = data.get('sms_title', project.sms_title)
        project.description = data.get('description', project.description)
        project.is_random_global = data.get('is_random_global', project.is_random_global)

        # 处理验证码正则表达式
        if 'code_regex_patterns' in data:
            code_regex_patterns = data.get('code_regex_patterns', '').strip()
            if code_regex_patterns:
                # 验证正则表达式格式
                try:
                    import re
                    patterns = [p.strip() for p in code_regex_patterns.split('\n') if p.strip()]
                    # 测试每个正则表达式是否有效
                    for pattern in patterns:
                        re.compile(pattern)
                    project.code_regex_patterns = '\n'.join(patterns)
                except re.error:
                    return jsonify({'error': '验证码正则表达式格式无效'}), 400
            else:
                project.code_regex_patterns = None

        db.session.commit()
        return jsonify({'success': True})

    elif request.method == 'DELETE':
        # 检查CSRF token
        token = request.headers.get('X-CSRF-Token')
        if not token or token != session.get('csrf_token'):
            return jsonify({'error': '无效的CSRF令牌'}), 403

        # 删除关联数据
        db.session.query(Card).filter_by(project_id=project_id).delete()
        db.session.query(UserNumber).filter_by(project_id=project_id).delete()
        db.session.query(DailyStats).filter_by(project_id=project_id).delete()
        db.session.delete(project)
        db.session.commit()
        return jsonify({'success': True})

# --- 号码组管理API ---
@app.route('/api/admin/groups', methods=['GET', 'POST'])
@admin_login_required
def admin_list_groups():
    if request.method == 'POST':
        data = request.json or {}
        project_id = data.get('project_id')
        name = data.get('name')
        token = data.get('token')
        if not all([project_id, name, token]):
            return jsonify({'error': '缺少必要参数'}), 400
        new_group = Group(
            project_id=project_id,
            name=name,
            token=token,
            order=data.get('order', 5),
            is_random=data.get('is_random', True)
        )
        db.session.add(new_group)
        db.session.commit()
        return jsonify({'success': True, 'group_id': new_group.id})

    # GET request
    project_id = request.args.get('project_id')
    if not project_id:
        return jsonify([])
    groups = db.session.execute(db.select(Group).filter_by(project_id=project_id).order_by(Group.order)).scalars().all()
    result = []
    for g in groups:
        total_nums = db.session.query(func.count(Number.id)).filter_by(group_id=g.id).scalar()
        used_nums = db.session.query(func.count(Number.id)).filter_by(group_id=g.id, is_used=True).scalar()
        disabled_nums = db.session.query(func.count(Number.id)).filter_by(group_id=g.id, is_disabled=True).scalar()
        available_nums = db.session.query(func.count(Number.id)).filter_by(group_id=g.id, is_used=False, is_disabled=False).scalar()
        result.append({
            'id': g.id,
            'name': g.name,
            'token': g.token,
            'is_disabled': g.is_disabled,
            'order': g.order,
            'is_random': g.is_random,
            'total_numbers': total_nums,
            'used_numbers': used_nums,
            'disabled_numbers': disabled_nums,
            'available_numbers': available_nums
        })
    return jsonify(result)

@app.route('/api/admin/groups/<int:group_id>', methods=['GET', 'PUT', 'DELETE'])
@admin_login_required
def manage_group(group_id):
    group = db.session.get(Group, group_id)
    if not group:
        return jsonify({'error': '号码组未找到'}), 404

    if request.method == 'GET':
        return jsonify({
            'id': group.id,
            'name': group.name,
            'token': group.token,
            'order': group.order,
            'is_random': group.is_random,
            'is_disabled': group.is_disabled
        })

    # PUT和DELETE方法需要CSRF token
    token = request.headers.get('X-CSRF-Token')
    if not token or token != session.get('csrf_token'):
        return jsonify({'error': '无效的CSRF令牌'}), 403

    if request.method == 'PUT':
        data = request.json or {}
        group.name = data.get('name', group.name)
        group.token = data.get('token', group.token)
        group.order = data.get('order', group.order)
        group.is_random = data.get('is_random', group.is_random)
        group.is_disabled = data.get('is_disabled', group.is_disabled)
        db.session.commit()
        return jsonify({'success': True})

    elif request.method == 'DELETE':
        db.session.delete(group)
        db.session.commit()
        return jsonify({'success': True})

# --- 改进的卡密管理API（合并生成和管理功能，支持分页）---
@app.route('/api/admin/cards', methods=['GET', 'POST'])
@admin_login_required
def admin_cards():
    if request.method == 'GET':
        project_id = request.args.get('project_id')
    else:
        # POST请求：先尝试JSON，再尝试表单数据
        if request.is_json and request.json:
            project_id = request.json.get('project_id')
        else:
            project_id = request.form.get('project_id')

    # 对于POST请求（生成卡密），project_id是必需的
    if request.method == 'POST' and not project_id:
        return jsonify({'error': '缺少项目ID'}), 400

    if request.method == 'POST':
        # 检查CSRF token（仅对表单提交）
        if request.form.get('csrf_token'):
            csrf_token = request.form.get('csrf_token')
            if not csrf_token or csrf_token != session.get('csrf_token'):
                return jsonify({'error': '无效的CSRF令牌'}), 403

        # 生成卡密
        # 支持JSON和表单两种提交方式
        if request.is_json and request.json:
            data = request.json
        else:
            # 表单数据
            data = request.form.to_dict()

        count = int(data.get('count', 1))
        download = data.get('download') in ['true', True, 'True', '1', 1]  # 是否需要下载

        if count > 1000:
            return jsonify({'error': '单次生成数量不能超过1000'}), 400

        cards = []
        card_objects = []
        for _ in range(count):
            random_code = ''.join(random.choices(string.ascii_lowercase + string.digits, k=10))
            card = Card(code=random_code, project_id=project_id)
            db.session.add(card)
            card_objects.append(card)
            cards.append(random_code)

        db.session.commit()

        # 更新统计
        update_daily_stats(project_id, cards_generated=count)

        response_data = {'success': True, 'cards': cards, 'count': count}

        # 如果需要下载，生成TXT文件
        if download:
            # 获取项目名称
            project = Project.query.get(project_id)
            project_name = project.name if project else f'project_{project_id}'

            output = io.StringIO()
            for card in cards:
                # 生成URL格式的卡密链接
                url = f'http://127.0.0.1:5600/?key={card}'
                output.write(url + '\n')

            output.seek(0)
            # 将项目名称转换为ASCII安全格式（移除特殊字符，保留字母数字和下划线）
            import re
            safe_project_name = re.sub(r'[^a-zA-Z0-9_]', '_', project_name)
            ascii_filename = f'{safe_project_name}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt'

            # 直接使用send_file，不设置复杂的Content-Disposition头
            return send_file(
                io.BytesIO(output.getvalue().encode('utf-8-sig')),
                mimetype='text/plain',
                as_attachment=True,
                download_name=ascii_filename
            )

        return jsonify(response_data)

    # GET request - 分页获取卡密列表
    page = int(request.args.get('page', 1))
    size = int(request.args.get('size', 20))
    is_used_param = request.args.get('is_used')
    keyword = request.args.get('q', '').strip()

    # 默认搜索全部项目的卡密
    card_query = db.select(Card, Project.name.label('project_name')).join(
        Project, Card.project_id == Project.id
    )

    # 如果指定了project_id，则只搜索该项目
    if project_id and project_id.strip():
        card_query = card_query.filter(Card.project_id == project_id)

    # 过滤条件
    if is_used_param is not None:
        if is_used_param.lower() == 'true':
            card_query = card_query.where(Card.is_used == True)
        elif is_used_param.lower() == 'false':
            card_query = card_query.where(Card.is_used == False)

    if keyword:
        card_query = card_query.where(
            db.or_(
                Card.code.contains(keyword),
                Card.number.contains(keyword),
                Card.sms_code.contains(keyword),
                Project.name.contains(keyword)  # 支持按项目名搜索
            )
        )

    # 获取总数
    total_query = db.select(func.count()).select_from(card_query.subquery())
    total = db.session.execute(total_query).scalar()

    # 分页查询
    results = db.session.execute(
        card_query.order_by(Card.create_time.desc())
        .offset((page - 1) * size)
        .limit(size)
    ).all()

    def dt_to_str(dt):
        return dt.isoformat(sep=' ', timespec='seconds') if dt else None

    return jsonify({
        'success': True,
        'cards': [{
            'code': result.Card.code,
            'is_used': result.Card.is_used,
            'create_time': dt_to_str(result.Card.create_time),
            'use_time': dt_to_str(result.Card.use_time),
            'number': result.Card.number,
            'sms_code': result.Card.sms_code,
            'sms_content': result.Card.sms_content,
            'project_name': result.project_name  # 添加项目名称
        } for result in results],
        'pagination': {
            'page': page,
            'size': size,
            'total': total,
            'pages': (total + size - 1) // size
        }
    })

@app.route('/api/admin/cards/<card_code>', methods=['DELETE'])
@admin_login_required
@require_token
def delete_card(card_code):
    card = db.session.execute(db.select(Card).filter_by(code=card_code)).scalars().first()
    if card:
        # 删除关联的UserNumber记录
        db.session.query(UserNumber).filter_by(card_code=card_code).delete()
        db.session.delete(card)
        db.session.commit()
        return jsonify({'success': True})
    return jsonify({'error': '卡密未找到'}), 404

# --- 号码管理API ---
@app.route('/api/admin/groups/<int:group_id>/numbers', methods=['GET', 'POST'])
@admin_login_required
def group_numbers(group_id):
    group = db.session.get(Group, group_id)
    if not group:
        return jsonify({'error': '号码组不存在'}), 404

    if request.method == 'POST':
        # 批量添加号码
        data = request.json or {}
        numbers_text = data.get('numbers', '').strip()
        if not numbers_text:
            return jsonify({'error': '号码数据不能为空'}), 400

        numbers_to_add = []
        for line in numbers_text.splitlines():
            line = line.strip()
            if not line:
                continue

            # 支持三种格式：COM4 17872306728、COM4,17872306728 或 纯号码 17872306728
            if ' ' in line and ',' not in line:
                # 格式：COM4 17872306728
                parts = line.split(' ', 1)
                if len(parts) == 2:
                    com_str, num_str = parts
                    com_match = re.search(r'\d+', com_str)
                    if com_match:
                        numbers_to_add.append(Number(
                            group_id=group_id,
                            com=int(com_match.group(0)),
                            number=num_str.strip()
                        ))
            elif ',' in line:
                # 格式：COM4,17872306728
                com_str, num_str = line.split(',', 1)
                com_match = re.search(r'\d+', com_str)
                if com_match:
                    numbers_to_add.append(Number(
                        group_id=group_id,
                        com=int(com_match.group(0)),
                        number=num_str.strip()
                    ))
            else:
                # 格式：纯号码 17872306728（使用默认COM值1）
                if re.match(r'^\d{11}$', line):  # 验证是11位数字
                    numbers_to_add.append(Number(
                        group_id=group_id,
                        com=1,  # 默认COM值
                        number=line
                    ))

        if not numbers_to_add:
            return jsonify({'error': '未检测到有效号码'}), 400

        db.session.bulk_save_objects(numbers_to_add)
        db.session.commit()
        return jsonify({'success': True, 'added_count': len(numbers_to_add)})

    # GET request - 获取号码列表
    page = int(request.args.get('page', 1))
    size = int(request.args.get('size', 50))
    status_filter = request.args.get('status')

    numbers_query = db.select(Number).filter_by(group_id=group_id)

    # 添加状态过滤
    if status_filter:
        if status_filter == 'disabled':
            numbers_query = numbers_query.filter(Number.is_disabled == True)
        elif status_filter == 'available':
            numbers_query = numbers_query.filter(Number.is_disabled == False, Number.status == 'available')
        elif status_filter == 'used':
            numbers_query = numbers_query.filter(Number.status == 'used')
        elif status_filter == 'in_use':
            numbers_query = numbers_query.filter(Number.status == 'in_use')

    total = db.session.execute(db.select(func.count()).select_from(numbers_query.subquery())).scalar()

    # 自定义排序：优先显示可用状态，然后按状态优先级排序
    # 排序优先级：可用(1) -> 使用中(2) -> 已使用(3) -> 已禁用(4)
    from sqlalchemy import case

    status_priority = case(
        (Number.is_disabled == True, 4),  # 已禁用
        (Number.status == 'available', 1),  # 可用
        (Number.status == 'in_use', 2),     # 使用中
        (Number.status == 'used', 3),       # 已使用
        else_=5  # 其他未知状态
    )

    numbers = db.session.execute(
        numbers_query.order_by(status_priority, Number.id)
        .offset((page - 1) * size)
        .limit(size)
    ).scalars().all()

    return jsonify({
        'success': True,
        'group': {'id': group.id, 'name': group.name, 'token': group.token},
        'numbers': [{
            'id': n.id,
            'number': n.number,
            'com': n.com,
            'status': n.status,
            'last_use_time': n.last_use_time.isoformat() if n.last_use_time else None,
            'is_disabled': n.is_disabled,
            'is_used': n.is_used
        } for n in numbers],
        'pagination': {
            'page': page,
            'size': size,
            'total': total,
            'pages': (total + size - 1) // size
        }
    })

# --- 单个号码操作API ---
@app.route('/api/admin/numbers/<int:number_id>', methods=['PUT', 'DELETE'])
@admin_login_required
@require_token
def manage_number(number_id):
    number = db.session.get(Number, number_id)
    if not number:
        return jsonify({'error': '号码未找到'}), 404

    if request.method == 'PUT':
        data = request.json or {}
        if 'is_disabled' in data:
            number.is_disabled = data['is_disabled']
            db.session.commit()
            return jsonify({'success': True})
        return jsonify({'error': '无效的更新参数'}), 400

    elif request.method == 'DELETE':
        db.session.delete(number)
        db.session.commit()
        return jsonify({'success': True})

# --- 号码搜索API ---
@app.route('/api/admin/numbers/search', methods=['GET'])
@admin_login_required
def search_numbers():
    """搜索号码 - 支持全项目搜索"""
    project_id = request.args.get('project_id', type=int)
    search_all = request.args.get('search_all', 'false').lower() == 'true'

    # 获取搜索参数
    query = request.args.get('q', '').strip()
    status_filter = request.args.get('status', '').strip()
    page = int(request.args.get('page', 1))
    size = int(request.args.get('size', 20))

    # 构建查询 - JOIN Number、Group和Project表
    if search_all or not project_id:
        # 搜索全部项目的号码
        numbers_query = db.select(
            Number,
            Group.name.label('group_name'),
            Project.name.label('project_name')
        ).join(Group, Number.group_id == Group.id).join(
            Project, Group.project_id == Project.id
        )
    else:
        # 搜索指定项目的号码
        project = db.session.execute(db.select(Project).filter_by(id=project_id)).scalars().first()
        if not project:
            return jsonify({'success': False, 'error': '项目不存在'})

        numbers_query = db.select(
            Number,
            Group.name.label('group_name'),
            Project.name.label('project_name')
        ).join(Group, Number.group_id == Group.id).join(
            Project, Group.project_id == Project.id
        ).filter(Group.project_id == project_id)

    # 添加号码搜索条件
    if query:
        numbers_query = numbers_query.filter(Number.number.like(f'%{query}%'))

    # 添加状态过滤
    if status_filter:
        if status_filter == 'disabled':
            numbers_query = numbers_query.filter(Number.is_disabled == True)
        elif status_filter == 'available':
            numbers_query = numbers_query.filter(Number.is_disabled == False, Number.status == 'available')
        elif status_filter == 'used':
            numbers_query = numbers_query.filter(Number.status == 'used')
        elif status_filter == 'in_use':
            numbers_query = numbers_query.filter(Number.status == 'in_use')

    # 计算总数
    total = db.session.execute(db.select(func.count()).select_from(numbers_query.subquery())).scalar()

    # 排序和分页
    from sqlalchemy import case
    status_priority = case(
        (Number.is_disabled == True, 4),  # 已禁用
        (Number.status == 'available', 1),  # 可用
        (Number.status == 'in_use', 2),     # 使用中
        (Number.status == 'used', 3),       # 已使用
        else_=5  # 其他未知状态
    )

    results = db.session.execute(
        numbers_query.order_by(status_priority, Number.id)
        .offset((page - 1) * size)
        .limit(size)
    ).all()

    # 格式化结果
    numbers_data = []
    for result in results:
        number = result[0]
        group_name = result[1]
        project_name = result[2] if len(result) > 2 else None

        # 确定显示状态
        if number.is_disabled:
            display_status = '已禁用'
        elif number.status == 'available':
            display_status = '可用'
        elif number.status == 'in_use':
            display_status = '使用中'
        elif number.status == 'used':
            display_status = '已使用'
        else:
            display_status = '未知'

        number_data = {
            'id': number.id,
            'number': number.number,
            'group_name': group_name,
            'status': display_status,
            'raw_status': number.status,
            'is_disabled': number.is_disabled,
            'last_used': number.last_use_time.strftime('%Y-%m-%d %H:%M:%S') if number.last_use_time else '-',
            'com': number.com
        }

        # 如果是全项目搜索，添加项目名称
        if search_all or not project_id:
            number_data['project_name'] = project_name

        numbers_data.append(number_data)

    # 分页信息
    pagination = {
        'page': page,
        'size': size,
        'total': total,
        'pages': (total + size - 1) // size
    }

    return jsonify({
        'success': True,
        'numbers': numbers_data,
        'pagination': pagination,
        'query': query,
        'status_filter': status_filter
    })

# --- 调试API ---
@app.route('/api/debug/font-status', methods=['GET'])
def debug_font_status():
    """调试字体加载状态"""
    import requests

    debug_info = {
        'timestamp': get_beijing_time().isoformat(),
        'cdn_status': {},
        'recommendations': [],
        'strategy': 'Pure CDN loading - no local files'
    }

    # 检查CDN状态
    cdn_urls = [
        'https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css',
        'https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2',
        'https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/webfonts/fa-brands-400.woff2'
    ]

    for url in cdn_urls:
        try:
            response = requests.head(url, timeout=5)
            file_type = url.split('/')[-1].split('.')[-1]
            debug_info['cdn_status'][file_type] = {
                'url': url,
                'status_code': response.status_code,
                'accessible': response.status_code == 200,
                'content_length': response.headers.get('content-length', 'unknown')
            }
            if response.status_code != 200:
                debug_info['recommendations'].append(f'CDN {file_type} 文件无法访问: {response.status_code}')
        except Exception as e:
            file_type = url.split('/')[-1].split('.')[-1]
            debug_info['cdn_status'][file_type] = {
                'url': url,
                'error': str(e),
                'accessible': False
            }
            debug_info['recommendations'].append(f'CDN {file_type} 连接失败: {str(e)}')

    # 生成建议
    if not debug_info['recommendations']:
        debug_info['recommendations'].append('所有资源看起来都正常')

    return jsonify(debug_info)

# --- 用户前台路由 ---
@app.route('/')
def root():
    key = request.args.get('key')
    import time
    timestamp = str(int(time.time()))  # 添加时间戳防止缓存
    if not key:
        return render_template('user.html', timestamp=timestamp)
    # 如果有key参数，直接处理
    return sms_token_entry()

@app.route('/sms')
def sms_token_entry():
    key = request.args.get('key')
    import time
    timestamp = str(int(time.time()))  # 添加时间戳防止缓存
    if not key:
        return render_template('user.html', timestamp=timestamp)

    card = db.session.execute(db.select(Card).filter_by(code=key)).scalars().first()
    if not card or not card.project:
        return render_template('user.html', error='卡密无效或项目不存在', timestamp=timestamp)

    project = card.project

    # 如果卡密已有验证码，直接展示
    if card.sms_code:
        use_time_str = card.use_time.strftime('%Y-%m-%d %H:%M:%S') if card.use_time else None
        # 将Markdown转换为HTML，并处理换行
        project_description_html = None
        if project.description:
            # 先处理换行符，然后转换Markdown
            description_with_breaks = project.description.replace('\n', '\n\n')
            project_description_html = markdown.markdown(description_with_breaks)

        return render_template('user.html',
                               key=key,
                               number=card.number,
                               sms_code=card.sms_code,
                               sms_content=card.sms_content,
                               is_used=card.is_used,
                               use_time=use_time_str,
                               project_name=project.name,
                               project_description=project_description_html,
                               ui_config=get_dynamic_ui_config(),
                               timestamp=timestamp)

    # 检查号码是否仍然有效
    assigned_number_obj = None
    if card.number:
        assigned_number_obj = db.session.execute(db.select(Number).filter_by(number=card.number)).scalars().first()
        if not assigned_number_obj or assigned_number_obj.is_disabled or assigned_number_obj.status not in ('available', 'in_use'):
            card.number = None
            card.com = None
            db.session.commit()

    user_number_id = None
    if card.number:
        user_number = db.session.execute(db.select(UserNumber).filter_by(card_code=key).order_by(UserNumber.id.desc())).scalars().first()
        if user_number:
            user_number_id = user_number.id

    # 处理项目描述的换行
    project_description_html = None
    if project.description:
        description_with_breaks = project.description.replace('\n', '\n\n')
        project_description_html = markdown.markdown(description_with_breaks)

    return render_template('user.html',
                           key=key,
                           number=card.number,
                           user_number_id=user_number_id,
                           show_get_number=not card.number,
                           project_name=project.name,
                           project_description=project_description_html,
                           ui_config=get_dynamic_ui_config(),
                           timestamp=timestamp)

# --- 用户API接口 ---
@app.route('/api/user/get_number', methods=['POST'])
def get_number():
    # IP速率限制检查
    client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', ''))
    if not check_ip_rate_limit(client_ip):
        return jsonify({'success': False, 'error': '请求过于频繁，请稍后再试'}), 429

    # 基础速率限制检查
    if not check_rate_limit(client_ip,
                           max_requests=RATE_LIMIT_MAX_REQUESTS,
                           window_minutes=RATE_LIMIT_WINDOW_MINUTES):
        return jsonify({'success': False, 'error': '请求过于频繁，请稍后再试'}), 429

    data = request.json or {}
    key = data.get('card')

    if not key or len(key) < 6 or len(key) > 32:
        return jsonify({'success': False, 'error': '卡密格式无效'})

    # 检查卡密1小时内使用次数限制
    if not check_card_hourly_limit(key):
        return jsonify({'success': False, 'error': f'该卡密1小时内使用次数已达上限({CARD_HOURLY_LIMIT}次)，请稍后再试'})

    card = db.session.execute(db.select(Card).filter_by(code=key)).scalars().first()
    if not card or not card.project or card.is_used:
        return jsonify({'success': False, 'error': '卡密无效或已被使用'})

    # 检查是否已经分配了号码
    if card.number:
        existing_user_number = db.session.execute(
            db.select(UserNumber).filter_by(card_code=key).order_by(UserNumber.id.desc())
        ).scalars().first()
        if existing_user_number:
            return jsonify({
                'success': True,
                'number': card.number,
                'user_number_id': existing_user_number.id,
                'message': '号码已分配'
            })

    # 查找可用号码
    available_groups = db.session.execute(
        db.select(Group).filter(
            Group.project_id == card.project_id,
            Group.is_disabled == False
        ).order_by(Group.order)
    ).scalars().all()

    number_to_assign = None

    if card.project.is_random_global:
        # 号码组随机取号
        group_ids = [group.id for group in available_groups]
        if group_ids:
            available_number_query = db.select(Number).filter(
                Number.group_id.in_(group_ids),
                Number.is_used == False,
                Number.is_disabled == False
            ).order_by(func.random())
            number_to_assign = db.session.execute(available_number_query).scalars().first()
    else:
        # 按组顺序取号
        for group in available_groups:
            available_number_query = db.select(Number).filter(
                Number.group_id == group.id,
                Number.is_used == False,
                Number.is_disabled == False
            ).order_by(Number.id)

            number_to_assign = db.session.execute(available_number_query).scalars().first()
            if number_to_assign:
                break

    if not number_to_assign:
        return jsonify({'success': False, 'error': '暂无号码，请联系客服'})

    try:
        # 分配号码
        db.session.refresh(number_to_assign)
        if number_to_assign.is_used or number_to_assign.is_disabled:
            db.session.rollback()
            return jsonify({'success': False, 'error': '号码已被占用，请重试'})

        number_to_assign.is_used = True
        number_to_assign.status = 'in_use'
        number_to_assign.last_use_time = get_beijing_time()

        card.number = number_to_assign.number
        card.com = number_to_assign.com

        user_number = UserNumber(card_code=key, number_id=number_to_assign.id, project_id=card.project_id)
        db.session.add(user_number)

        # 创建或更新卡密与号码的绑定
        existing_binding = db.session.execute(
            db.select(CardNumberBinding).filter_by(card_code=key)
        ).scalars().first()

        if existing_binding:
            # 更新现有绑定
            existing_binding.number_id = number_to_assign.id
            existing_binding.project_id = card.project_id
            existing_binding.expires_at = get_beijing_time() + timedelta(minutes=CARD_NUMBER_BINDING_TIMEOUT)
        else:
            # 创建新绑定
            binding = CardNumberBinding(
                card_code=key,
                number_id=number_to_assign.id,
                project_id=card.project_id,
                expires_at=get_beijing_time() + timedelta(minutes=CARD_NUMBER_BINDING_TIMEOUT)
            )
            db.session.add(binding)

        db.session.commit()

        # 记录卡密使用
        record_card_usage(key, 'get_number', client_ip)

        return jsonify({'success': True, 'number': card.number, 'user_number_id': user_number.id})

    except Exception:
        db.session.rollback()
        return jsonify({'success': False, 'error': '分配号码失败，请重试'})

@app.route('/api/user/get_code', methods=['GET'])
def get_code():
    user_number_id = request.args.get('user_number_id')

    if not user_number_id or not user_number_id.isdigit():
        return jsonify({'status': 'error', 'error': '无效的号码记录ID'})

    user_number = db.session.get(UserNumber, user_number_id)
    if not user_number:
        return jsonify({'status': 'error', 'error': '号码记录不存在'})

    # 检查号码记录是否过期（超过30分钟）
    if user_number.created_at and (get_beijing_time() - user_number.created_at).total_seconds() > 1800:
        return jsonify({'status': 'error', 'error': '号码记录已过期'})

    card = db.session.execute(db.select(Card).filter_by(code=user_number.card_code)).scalars().first()
    if not card:
        return jsonify({'status': 'error', 'error': '关联的卡密不存在'})

    # 如果数据库中已经有验证码，直接返回
    if card.sms_code:
        return jsonify({'sms_code': card.sms_code, 'sms_content': card.sms_content})

    # 查询外部API
    number_obj = db.session.get(Number, user_number.number_id)
    if not number_obj:
        return jsonify({'status': 'error', 'error': '关联的号码不存在'})

    group = db.session.get(Group, number_obj.group_id)
    project = db.session.get(Project, user_number.project_id)

    if not group or not project:
        return jsonify({'status': 'error', 'error': '数据关联错误，找不到项目或号码组'})

    if not group.token or not project.sms_title:
        return jsonify({'status': 'error', 'error': '项目或号码组配置不完整'})

    try:
        response = requests.get(
            f"{SMS_API_HOST}/api/smslist",
            params={"token": group.token},
            headers={"Host": "sms.newszfang.vip:3000"},
            timeout=10
        )
        response.raise_for_status()
        data = response.json()

        if isinstance(data, list):
            if len(data) == 0:
                if ENABLE_SMS_DEBUG_LOG:
                    print("[SMS调试] API返回空列表")
                return jsonify({'status': 'waiting'})

            # 有数据的情况
            get_number_time = user_number.created_at

            if ENABLE_SMS_DEBUG_LOG:
                print(f"[SMS调试] API返回数据: {len(data)}条短信")
                print(f"[SMS调试] 项目短信标题: {project.sms_title}")
                print(f"[SMS调试] 目标号码: {number_obj.number}, COM: {number_obj.com}")

            # 获取id最大值（通常是第一个元素）
            max_id = max(sms_item.get('id', 0) for sms_item in data)
            if ENABLE_SMS_DEBUG_LOG:
                print(f"[SMS调试] 最大ID: {max_id}")

            for sms_item in data:
                sms_id = sms_item.get('id', 0)
                content = sms_item.get('content', '')
                sms_simnum = clean_bom_characters(sms_item.get('simnum', ''))

                if ENABLE_SMS_DEBUG_LOG:
                    print(f"[SMS调试] 检查短信ID: {sms_id}, simnum: '{sms_simnum}', content: {content[:50]}...")

                # 条件1: 检查是否是最新的短信（id最大）
                if sms_id != max_id:
                    if ENABLE_SMS_DEBUG_LOG:
                        print(f"[SMS调试] 跳过非最新短信，ID: {sms_id}")
                    continue

                # 条件2: 匹配项目的短信标题
                if project.sms_title not in content:
                    if ENABLE_SMS_DEBUG_LOG:
                        print(f"[SMS调试] 短信标题不匹配: '{project.sms_title}' 不在 '{content}' 中")
                    continue

                # 条件3: 匹配手机号码的前2位和后4位（simnum字段）
                simnum_matches = False
                if sms_simnum and len(sms_simnum) >= 6:
                    simnum_prefix = sms_simnum[:2]
                    simnum_suffix = sms_simnum[-4:]

                    if (len(number_obj.number) >= 6 and
                        number_obj.number.startswith(simnum_prefix) and
                        number_obj.number.endswith(simnum_suffix)):
                        simnum_matches = True
                        if ENABLE_SMS_DEBUG_LOG:
                            simnum_display = f"{simnum_prefix}****{simnum_suffix}" if '*' in sms_simnum else f"{simnum_prefix}...{simnum_suffix}"
                            print(f"[SMS调试] simnum匹配成功: {simnum_display} 匹配 {number_obj.number}")
                    else:
                        if ENABLE_SMS_DEBUG_LOG:
                            simnum_display = f"{simnum_prefix}****{simnum_suffix}" if '*' in sms_simnum else f"{simnum_prefix}...{simnum_suffix}"
                            print(f"[SMS调试] simnum不匹配: {simnum_display} 不匹配 {number_obj.number}")
                else:
                    if ENABLE_SMS_DEBUG_LOG:
                        print(f"[SMS调试] simnum格式无效（长度不足6位）: '{sms_simnum}'")

                # 三个条件都满足才继续处理
                if not simnum_matches:
                    if ENABLE_SMS_DEBUG_LOG:
                        print("[SMS调试] simnum不匹配，跳过此短信")
                    continue

                if ENABLE_SMS_DEBUG_LOG:
                    print("[SMS调试] 所有条件匹配成功，开始提取验证码")

                # 提取验证码 - 优先使用项目的正则表达式，否则使用配置文件中的
                sms_code = None
                patterns_to_use = CODE_REGEX_PATTERNS  # 默认使用配置文件中的

                # 如果项目有自定义的正则表达式，则使用项目的
                if project.code_regex_patterns:
                    patterns_to_use = [p.strip() for p in project.code_regex_patterns.split('\n') if p.strip()]

                if ENABLE_SMS_DEBUG_LOG:
                    print(f"[SMS调试] 使用正则表达式: {patterns_to_use}")

                for pattern in patterns_to_use:
                    try:
                        code_match = re.search(pattern, content)
                        if code_match:
                            sms_code = code_match.group(1)
                            if ENABLE_SMS_DEBUG_LOG:
                                print(f"[SMS调试] 正则匹配成功: 模式'{pattern}' 提取到验证码'{sms_code}'")
                            break
                        else:
                            if ENABLE_SMS_DEBUG_LOG:
                                print(f"[SMS调试] 正则模式'{pattern}'未匹配")
                    except re.error:
                        # 如果正则表达式有错误，跳过这个模式
                        if ENABLE_SMS_DEBUG_LOG:
                            print(f"[SMS调试] 正则表达式错误: {pattern}")
                        continue

                if sms_code:
                    if ENABLE_SMS_DEBUG_LOG:
                        print(f"[SMS调试] 成功提取验证码: {sms_code}")
                    card.sms_code = sms_code
                    card.sms_content = content
                    card.is_used = True
                    use_time = get_beijing_time()
                    card.use_time = use_time
                    number_obj.status = 'used'
                    number_obj.is_used = True
                    db.session.commit()

                    # 更新统计
                    update_daily_stats(card.project_id, cards_used=1)

                    return jsonify({
                        'sms_code': sms_code,
                        'sms_content': content,
                        'use_time': use_time.strftime('%Y-%m-%d %H:%M:%S'),
                        'is_used': True,
                        'match_type': 'simnum'
                    })
                else:
                    # 找到匹配的短信但无法提取验证码
                    if ENABLE_SMS_DEBUG_LOG:
                        print("[SMS调试] 找到匹配短信但无法提取验证码")
                    card.sms_content = content
                    card.is_used = True
                    use_time = get_beijing_time()
                    card.use_time = use_time
                    number_obj.status = 'used'
                    number_obj.is_used = True
                    db.session.commit()

                    # 更新统计
                    update_daily_stats(card.project_id, cards_used=1)

                    return jsonify({
                        'sms_content': content,
                        'use_time': use_time.strftime('%Y-%m-%d %H:%M:%S'),
                        'is_used': True,
                        'match_type': 'simnum',
                        'manual_extract': True
                    })

        if ENABLE_SMS_DEBUG_LOG:
            print("[SMS调试] 未找到匹配的短信，继续等待")
        return jsonify({'status': 'waiting'})

    except requests.exceptions.Timeout:
        return jsonify({'status': 'waiting', 'error': 'API请求超时'})
    except requests.exceptions.RequestException as e:
        return jsonify({'status': 'waiting', 'error': f'API请求失败: {str(e)}'})

@app.route('/api/user/replace_number', methods=['POST'])
def replace_number():
    """更换号码API"""
    # IP速率限制检查
    client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', ''))
    if not check_ip_rate_limit(client_ip):
        return jsonify({'success': False, 'error': '请求过于频繁，请稍后再试'}), 429

    # 基础速率限制检查
    if not check_rate_limit(client_ip, max_requests=3, window_minutes=5):
        return jsonify({'success': False, 'error': '请求过于频繁，请稍后再试'}), 429

    data = request.json or {}
    card_code = data.get('card_code')
    user_number_id = data.get('user_number_id')

    if not card_code or not user_number_id:
        return jsonify({'success': False, 'error': '参数不完整'})

    # 检查卡密1小时内使用次数限制
    if not check_card_hourly_limit(card_code):
        return jsonify({'success': False, 'error': f'该卡密1小时内使用次数已达上限({CARD_HOURLY_LIMIT}次)，请稍后再试'})

    try:
        # 验证卡密
        card = db.session.execute(db.select(Card).filter_by(code=card_code)).scalars().first()
        if not card or not card.project:
            return jsonify({'success': False, 'error': '卡密无效或项目不存在'})

        # 验证用户号码记录
        user_number = db.session.get(UserNumber, user_number_id)
        if not user_number or user_number.card_code != card_code:
            return jsonify({'success': False, 'error': '号码记录无效'})

        # 获取当前分配的号码
        current_number = db.session.get(Number, user_number.number_id)
        if current_number:
            # 更换号码后，之前的号码状态变更为禁用
            current_number.is_disabled = True
            current_number.status = 'disabled'
            current_number.is_used = False

        # 清理卡密中的号码信息
        card.number = None
        card.com = None

        # 删除当前的用户号码记录
        db.session.delete(user_number)
        db.session.commit()

        # 查找新的可用号码
        available_groups = db.session.execute(
            db.select(Group).filter(
                Group.project_id == card.project_id,
                Group.is_disabled == False
            ).order_by(Group.order)
        ).scalars().all()

        number_to_assign = None

        if card.project.is_random_global:
            # 号码组随机取号
            group_ids = [group.id for group in available_groups]
            if group_ids:
                available_number_query = db.select(Number).filter(
                    Number.group_id.in_(group_ids),
                    Number.is_used == False,
                    Number.is_disabled == False
                ).order_by(func.random())
                number_to_assign = db.session.execute(available_number_query).scalars().first()
        else:
            # 按组顺序取号
            for group in available_groups:
                available_number_query = db.select(Number).filter(
                    Number.group_id == group.id,
                    Number.is_used == False,
                    Number.is_disabled == False
                ).order_by(Number.id)

                number_to_assign = db.session.execute(available_number_query).scalars().first()
                if number_to_assign:
                    break

        if not number_to_assign:
            return jsonify({'success': False, 'error': '暂无可用号码，请联系客服'})

        # 分配新号码
        number_to_assign.is_used = True
        number_to_assign.status = 'in_use'
        number_to_assign.last_use_time = get_beijing_time()

        card.number = number_to_assign.number
        card.com = number_to_assign.com

        # 创建新的用户号码记录
        new_user_number = UserNumber(
            card_code=card_code,
            number_id=number_to_assign.id,
            project_id=card.project_id
        )
        db.session.add(new_user_number)

        # 更新卡密与号码的绑定
        existing_binding = db.session.execute(
            db.select(CardNumberBinding).filter_by(card_code=card_code)
        ).scalars().first()

        if existing_binding:
            # 更新现有绑定到新号码
            existing_binding.number_id = number_to_assign.id
            existing_binding.project_id = card.project_id
            existing_binding.expires_at = get_beijing_time() + timedelta(minutes=CARD_NUMBER_BINDING_TIMEOUT)
        else:
            # 创建新绑定
            binding = CardNumberBinding(
                card_code=card_code,
                number_id=number_to_assign.id,
                project_id=card.project_id,
                expires_at=get_beijing_time() + timedelta(minutes=CARD_NUMBER_BINDING_TIMEOUT)
            )
            db.session.add(binding)

        db.session.commit()

        # 记录卡密使用
        record_card_usage(card_code, 'replace_number', client_ip)

        return jsonify({
            'success': True,
            'number': number_to_assign.number,
            'user_number_id': new_user_number.id,
            'message': '号码更换成功'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': f'更换号码失败: {str(e)}'})

@app.route('/api/user/timeout_number', methods=['POST'])
def timeout_number():
    """验证码倒计时结束后禁用号码"""
    data = request.json or {}
    user_number_id = data.get('user_number_id')

    if not user_number_id:
        return jsonify({'success': False, 'error': '参数不完整'})

    try:
        user_number = db.session.get(UserNumber, user_number_id)
        if not user_number:
            return jsonify({'success': False, 'error': '号码记录不存在'})

        # 获取关联的号码
        number_obj = db.session.get(Number, user_number.number_id)
        if number_obj and number_obj.status != 'used':
            # 只有未获取到验证码的号码才禁用
            number_obj.status = 'disabled'
            number_obj.is_disabled = True

            # 解除卡密绑定
            binding = db.session.execute(
                db.select(CardNumberBinding).filter_by(card_code=user_number.card_code)
            ).scalars().first()
            if binding:
                db.session.delete(binding)

            db.session.commit()
            return jsonify({'success': True, 'message': '号码已禁用'})

        return jsonify({'success': True, 'message': '号码状态无需更改'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': f'操作失败: {str(e)}'})

# --- 应用初始化和启动 ---
def initialize_database():
    """初始化数据库和管理员用户"""
    with app.app_context():
        db.create_all()
        admin_exists = db.session.execute(db.select(Admin).limit(1)).scalars().first()
        if not admin_exists:
            default_admin = Admin(username='admin')
            default_admin.set_password(ADMIN_DEFAULT_PASSWORD)
            db.session.add(default_admin)
            print(f"默认管理员账户已创建: admin/{ADMIN_DEFAULT_PASSWORD}")
        db.session.commit()

def start_cleanup_tasks():
    """启动后台清理任务"""
    def cleanup_loop():
        while True:
            try:
                with app.app_context():
                    # 自动禁用超时号码
                    cutoff = get_beijing_time() - timedelta(minutes=3)
                    timeout_numbers = db.session.execute(
                        db.select(Number).where(
                            Number.status == 'in_use',
                            Number.last_use_time != None,
                            Number.last_use_time < cutoff
                        )
                    ).scalars().all()

                    if timeout_numbers:
                        for num in timeout_numbers:
                            num.is_used = False
                            num.is_disabled = True
                            num.status = 'used'
                            # 清理关联记录
                            user_numbers = db.session.execute(db.select(UserNumber).filter_by(number_id=num.id)).scalars().all()
                            for un in user_numbers:
                                card_obj = db.session.execute(db.select(Card).filter_by(code=un.card_code)).scalars().first()
                                if card_obj and not card_obj.sms_code:
                                    card_obj.number = None
                                    card_obj.com = None
                                db.session.delete(un)
                        db.session.commit()

                    # 清理过期卡密
                    retention_days = CARD_RETENTION_DAYS
                    if retention_days > 0:
                        cutoff_time = get_beijing_time() - timedelta(days=retention_days)
                        expired_cards = db.session.execute(
                            db.select(Card).where(
                                Card.is_used == True,
                                Card.use_time != None,
                                Card.use_time < cutoff_time
                            )
                        ).scalars().all()

                        if expired_cards:
                            for card in expired_cards:
                                db.session.query(UserNumber).filter_by(card_code=card.code).delete()
                                db.session.delete(card)
                            db.session.commit()

                    # 清理过期的卡密号码绑定
                    current_time = get_beijing_time()
                    expired_bindings = db.session.execute(
                        db.select(CardNumberBinding).filter(
                            CardNumberBinding.expires_at < current_time
                        )
                    ).scalars().all()

                    if expired_bindings:
                        for binding in expired_bindings:
                            # 禁用绑定的号码
                            number = db.session.execute(
                                db.select(Number).filter_by(id=binding.number_id)
                            ).scalars().first()

                            if number:
                                number.status = 'disabled'
                                number.is_disabled = True

                            # 删除绑定记录
                            db.session.delete(binding)

                        db.session.commit()
                        print(f"[清理任务] 清理了 {len(expired_bindings)} 个过期绑定")

            except Exception as e:
                print(f"[清理任务] 出错: {e}")

            time.sleep(300)  # 每5分钟检查一次

    Thread(target=cleanup_loop, daemon=True).start()

if __name__ == '__main__':
    initialize_database()
    start_cleanup_tasks()

    # 显示访问地址
    port = SERVER_CONFIG['PORT']
    print("\n" + "="*60)
    print("🚀 SMS验证码系统 V2 启动成功!")
    print("="*60)
    print(f"📱 用户前台: http://localhost:{port}/?key=卡密")
    print(f"🔧 管理后台: http://localhost:{port}/admin")
    print(f"👤 默认账号: admin / admin123")
    print(f"🌐 监听地址: {SERVER_CONFIG['HOST']}:{port}")
    print("="*60)
    print("💡 提示: 按 Ctrl+C 停止服务")
    print("="*60 + "\n")

    app.run(
        host=SERVER_CONFIG['HOST'],
        port=SERVER_CONFIG['PORT'],
        debug=SERVER_CONFIG['DEBUG'],
        threaded=SERVER_CONFIG['THREADED']
    )
