<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>SMS验证码系统 - 管理后台</title>

    <!-- Font Awesome图标库 - 优先使用CDN -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css" id="cdn-fa" onload="console.log('Font Awesome loaded')" onerror="console.error('Font Awesome CDN failed')">

    <!-- SVG图标样式 -->
    <style>
        /* SVG图标基础样式 */
        .fa, .fas, .far, .fab {
            display: inline-block;
            width: 1em;
            height: 1em;
            vertical-align: -0.125em;
            text-align: center;
        }

        /* SVG图标对齐样式 */
        .sidebar-item i,
        .stat-card-icon i,
        .btn i,
        .card-header i {
            vertical-align: middle;
            line-height: 1;
        }


    </style>
    <link rel="stylesheet" href="/static/css/admin.css">
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-shield-alt"></i> 管理后台</h3>
            </div>
            
            <!-- 项目选择器 -->
            <div class="project-selector">
                <select id="global-project-list" class="form-control">
                    <option value="">选择项目</option>
                </select>
            </div>
            
            <div class="sidebar-menu">
                <div class="sidebar-item active" data-route="/dashboard">
                    <i class="fas fa-chart-bar"></i>
                    <span>统计仪表板</span>
                </div>
                <div class="sidebar-item" data-route="/projects">
                    <i class="fas fa-folder"></i>
                    <span>项目管理</span>
                </div>
                <div class="sidebar-item" data-route="/groups">
                    <i class="fas fa-mobile-alt"></i>
                    <span>号码组管理</span>
                </div>
                <div class="sidebar-item" data-route="/search">
                    <i class="fas fa-search"></i>
                    <span>号码搜索</span>
                </div>
                <div class="sidebar-item" data-route="/cards">
                    <i class="fas fa-key"></i>
                    <span>卡密管理</span>
                </div>
            </div>
            
            <div class="sidebar-footer">

                <a href="/admin/logout">
                    <i class="bi bi-box-arrow-right"></i>
                    <span>退出登录</span>
                </a>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <div class="content-header">
                <h1 id="page-title">统计仪表板</h1>
            </div>
            
            <div class="content-body">
                <!-- 统计仪表板页面 -->
                <div id="dashboard-page" class="page active">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-card-header">
                                <div class="stat-card-icon warning">
                                    <i class="fas fa-calendar-day"></i>
                                </div>
                                <div>
                                    <div class="stat-card-title">今日使用量</div>
                                    <div class="stat-card-value" id="today-used">0</div>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-card-header">
                                <div class="stat-card-icon info">
                                    <i class="fas fa-calendar-minus"></i>
                                </div>
                                <div>
                                    <div class="stat-card-title">昨日使用量</div>
                                    <div class="stat-card-value" id="yesterday-used">0</div>
                                </div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-card-header">
                                <div class="stat-card-icon success">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div>
                                    <div class="stat-card-title">总使用卡密</div>
                                    <div class="stat-card-value" id="total-used-cards">0</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">项目使用排行</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>排名</th>
                                            <th>项目名称</th>
                                            <th>今日使用</th>
                                            <th>已使用</th>
                                        </tr>
                                    </thead>
                                    <tbody id="ranking-table">
                                        <tr>
                                            <td colspan="4" class="text-center">加载中...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 项目管理页面 -->
                <div id="projects-page" class="page">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">项目管理</h3>
                            <button class="btn btn-primary" onclick="showAddProjectModal()">
                                <i class="bi bi-plus-lg"></i> 添加项目
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>项目名称</th>
                                            <th>可用卡密</th>
                                            <th>总卡密</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="projects-table">
                                        <tr>
                                            <td colspan="6" class="text-center">加载中...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 号码组管理页面 -->
                <div id="groups-page" class="page">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">号码组管理</h3>
                            <div class="header-controls">
                                <!-- 号码组随机取号开关 -->
                                <div class="global-random-control" id="global-random-control" style="display: none;">
                                    <label class="switch-label">号码组随机取号</label>
                                    <label class="switch">
                                        <input type="checkbox" id="global-random-switch">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <button class="btn btn-primary" onclick="showAddGroupModal()" id="add-group-btn" disabled>
                                    <i class="fas fa-plus"></i> 添加号码组
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="groups-content">
                                <p class="text-center">请先选择一个项目</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 卡密管理页面 -->
                <div id="cards-page" class="page">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">卡密管理</h3>
                            <div class="header-controls">
                                <div class="search-all-control">
                                    <label class="switch-label">搜索全部项目</label>
                                    <label class="switch">
                                        <input type="checkbox" id="cards-search-all-projects-switch" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <button class="btn btn-primary" onclick="showGenerateCardsModal()" id="generate-cards-btn" disabled>
                                    <i class="bi bi-plus-lg"></i> 生成卡密
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="row">
                                    <div class="col-md-4">
                                        <select id="cards-filter" class="form-control">
                                            <option value="">全部卡密</option>
                                            <option value="false">未使用</option>
                                            <option value="true">已使用</option>
                                        </select>
                                    </div>
                                    <div class="col-md-8">
                                        <input type="text" id="cards-search" class="form-control" placeholder="搜索卡密、号码、验证码或项目名...">
                                    </div>
                                </div>
                            </div>
                            <div id="cards-content">
                                <p class="text-center">请先选择一个项目</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 号码搜索页面 -->
                <div id="search-page" class="page">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">号码搜索</h3>
                            <div class="header-controls">
                                <div class="search-all-control">
                                    <label class="switch-label">搜索全部项目</label>
                                    <label class="switch">
                                        <input type="checkbox" id="search-all-projects-switch" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="row">
                                    <div class="col-md-6">
                                        <input type="text" id="number-search" class="form-control" placeholder="输入手机号码搜索...">
                                    </div>
                                    <div class="col-md-3">
                                        <select id="number-status-filter" class="form-control">
                                            <option value="">全部状态</option>
                                            <option value="available">可用</option>
                                            <option value="in_use">使用中</option>
                                            <option value="used">已使用</option>
                                            <option value="disabled">已禁用</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-primary btn-block" onclick="searchNumbers()">
                                            <i class="bi bi-search"></i> 搜索
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div id="search-results">
                                <p class="text-center text-muted">请先选择项目并输入搜索条件</p>
                            </div>

                            <!-- 搜索结果表格 -->
                            <div id="search-results-table" style="display: none;">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <span id="search-stats" class="text-muted"></span>
                                    </div>
                                </div>

                                <div class="table-container" style="max-height: 500px; overflow-y: auto;">
                                    <table class="table table-sm">
                                        <thead class="table-header-sticky">
                                            <tr>
                                                <th>ID</th>
                                                <th>号码</th>
                                                <th>归属号码组</th>
                                                <th>状态</th>
                                                <th>最后使用时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="search-results-tbody">
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 搜索结果分页 -->
                                <div id="search-pagination" class="d-flex justify-content-center mt-3">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <!-- 项目模态框 -->
    <div id="project-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="project-modal-title">添加项目</h3>
                <button class="modal-close" onclick="closeProjectModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="project-form">
                    <div class="form-group">
                        <label class="form-label">项目名称 *</label>
                        <input type="text" id="project-name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">短信标题 *</label>
                        <input type="text" id="project-sms-title" class="form-control" required>
                        <div class="form-text">用于匹配短信内容的关键词</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">项目描述</label>
                        <textarea id="project-description" class="form-control" rows="4" placeholder="支持Markdown格式，换行会自动处理"></textarea>
                        <div class="form-text">将显示在用户前台，支持Markdown格式</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">验证码正则表达式</label>
                        <textarea id="project-code-regex" class="form-control" rows="3" placeholder="每行一个正则表达式，留空则使用系统默认"></textarea>
                        <div class="form-text">用于从短信中提取验证码，每行一个正则表达式，按优先级排序。留空则使用系统默认配置</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeProjectModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveProject()">保存</button>
            </div>
        </div>
    </div>

    <!-- 号码组模态框 -->
    <div id="group-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="group-modal-title">添加号码组</h3>
                <button class="modal-close" onclick="closeGroupModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="group-form">
                    <div class="form-group">
                        <label class="form-label">号码组名称 *</label>
                        <input type="text" id="group-name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">API Token *</label>
                        <input type="text" id="group-token" class="form-control" required>
                        <div class="form-text">短信API的访问令牌</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">优先级</label>
                        <input type="number" id="group-order" class="form-control" value="5" min="1" max="10">
                        <div class="form-text">数字越小优先级越高</div>
                    </div>

                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeGroupModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveGroup()">保存</button>
            </div>
        </div>
    </div>

    <!-- 生成卡密模态框 -->
    <div id="cards-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>生成卡密</h3>
                <button class="modal-close" onclick="closeCardsModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="cards-form">
                    <div class="form-group">
                        <label class="form-label">生成数量 *</label>
                        <input type="number" id="cards-count" class="form-control" value="1" min="1" max="1000" required>
                        <div class="form-text">单次最多生成1000个卡密</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">
                            <input type="checkbox" id="cards-download"> 生成后下载文件
                        </label>
                        <div class="form-text">生成完成后自动下载txt格式的卡密文件</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeCardsModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="generateCards()">生成卡密</button>
            </div>
        </div>
    </div>

    <!-- 模态框遮罩 -->
    <div id="modal-overlay" class="modal-overlay"></div>

    <!-- 隐藏的CSRF Token -->
    <input type="hidden" id="csrf-token" value="{{ csrf_token }}">

    <!-- 添加号码模态框 -->
    <div id="add-numbers-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="add-numbers-modal-title">添加号码</h3>
                <button class="modal-close" onclick="closeAddNumbersModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="add-numbers-form">
                    <div class="form-group">
                        <label class="form-label">号码列表 *</label>
                        <textarea id="numbers-text" class="form-control" rows="10" placeholder="支持以下格式，每行一个号码：&#10;COM3,14547739282&#10;COM115 14547739282&#10;14547739282" required></textarea>
                        <div class="form-text">
                            支持三种格式：<br>
                            • COM3,14547739282<br>
                            • COM115 14547739282<br>
                            • 14547739282（纯号码，将使用默认COM值）
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeAddNumbersModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveNumbers()">添加号码</button>
            </div>
        </div>
    </div>

    <!-- 查看号码模态框 -->
    <div id="view-numbers-modal" class="modal">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h3 id="view-numbers-modal-title">号码列表</h3>
                <button class="modal-close" onclick="closeViewNumbersModal()">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 快速排序按钮 -->
                <div class="mb-3">
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-secondary active" id="sort-default" onclick="resetNumbersFilter()">默认排序</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="sort-available" onclick="filterNumbers('available')">可用优先</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="sort-in-use" onclick="filterNumbers('in_use')">使用中优先</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="sort-used" onclick="filterNumbers('used')">已使用优先</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="sort-disabled" onclick="filterNumbers('disabled')">已禁用优先</button>
                    </div>
                </div>

                <!-- 号码表格 -->
                <div class="table-container" style="max-height: 400px; overflow-y: auto;">
                    <table class="table table-sm">
                        <thead class="table-header-sticky">
                            <tr>
                                <th>ID</th>
                                <th>号码</th>
                                <th>状态</th>
                                <th>最后使用时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="numbers-table-body">
                            <tr>
                                <td colspan="5" class="text-center">加载中...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 分页控件 -->
                <div id="numbers-pagination" class="pagination-container mt-3">
                    <!-- 分页按钮将在这里动态生成 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeViewNumbersModal()">关闭</button>
            </div>
        </div>
    </div>

    <!-- 脚本文件 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="/static/js/utils.js"></script>
    <script src="/static/js/admin.js"></script>

    <!-- 智能图标系统 - 优先CDN，失败时自动切换SVG -->
    <script src="/static/js/icons.js"></script>








</body>
</html>
