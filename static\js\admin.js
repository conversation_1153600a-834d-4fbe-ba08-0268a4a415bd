// 管理后台主脚本
class AdminApp {
    constructor() {
        this.router = new Router();
        this.currentProject = null;
        this.currentProjectId = null;
        this.cardsPagination = null;
        this.groupsPagination = null;
        this.init();
    }

    init() {
        // 设置CSRF Token
        window.csrfToken = document.getElementById('csrf-token').value;

        // 初始化路由
        this.setupRoutes();

        // 绑定事件
        this.bindEvents();

        // 加载初始数据
        this.loadProjects();

        // 预加载仪表板数据（在后台进行）
        this.preloadDashboardData();

        // 恢复项目选择状态
        const savedProjectId = localStorage.getItem('adminCurrentProjectId');
        const savedProjectName = localStorage.getItem('adminCurrentProjectName');
        if (savedProjectId) {
            this.currentProjectId = savedProjectId;
            this.currentProject = savedProjectName;
            setTimeout(() => {
                const projectSelect = document.getElementById('global-project-list');
                if (projectSelect) {
                    projectSelect.value = savedProjectId;
                    this.onProjectChange();
                }
            }, 500);
        }
    }

    setupRoutes() {
        this.router.addRoute('/dashboard', () => this.showPage('dashboard'));
        this.router.addRoute('/projects', () => this.showPage('projects'));
        this.router.addRoute('/groups', () => this.showPage('groups'));
        this.router.addRoute('/cards', () => this.showPage('cards'));
        this.router.addRoute('/search', () => this.showPage('search'));
    }

    bindEvents() {
        // 侧边栏点击事件
        document.querySelectorAll('.sidebar-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const route = e.currentTarget.dataset.route;
                if (route) {
                    this.router.navigate(route);
                }
            });
        });

        // 项目选择事件
        document.getElementById('global-project-list').addEventListener('change', (e) => {
            this.currentProjectId = e.target.value;
            this.currentProject = e.target.options[e.target.selectedIndex].text;

            if (this.currentProjectId) {
                localStorage.setItem('adminCurrentProjectId', this.currentProjectId);
                localStorage.setItem('adminCurrentProjectName', this.currentProject);
            } else {
                localStorage.removeItem('adminCurrentProjectId');
                localStorage.removeItem('adminCurrentProjectName');
            }

            this.onProjectChange();
        });

        // 号码组随机取号开关事件
        document.getElementById('global-random-switch').addEventListener('change', (e) => {
            this.toggleGlobalRandom(e.target.checked);
        });



        // 卡密搜索和过滤 - 延迟绑定，确保元素存在
        setTimeout(() => {
            const searchInput = document.getElementById('cards-search');
            const filterSelect = document.getElementById('cards-filter');
            const cardsSearchAllSwitch = document.getElementById('cards-search-all-projects-switch');
            const numberSearchAllSwitch = document.getElementById('search-all-projects-switch');

            if (searchInput) {
                searchInput.addEventListener('input', this.debounce(() => this.loadCards(), 500));
            }
            if (filterSelect) {
                filterSelect.addEventListener('change', () => this.loadCards());
            }
            if (cardsSearchAllSwitch) {
                cardsSearchAllSwitch.addEventListener('change', () => this.loadCards());
            }
            if (numberSearchAllSwitch) {
                numberSearchAllSwitch.addEventListener('change', () => {
                    // 清空搜索结果，等待用户重新搜索
                    document.getElementById('search-results').innerHTML = '<p class="text-center text-muted">请输入搜索条件</p>';
                    document.getElementById('search-results-table').style.display = 'none';
                });
            }
        }, 100);

        // 模态框事件
        document.addEventListener('click', (e) => {
            // 点击模态框背景关闭模态框
            if (e.target.classList.contains('modal')) {
                this.closeAllModals();
            }
        });

        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });
    }

    closeAllModals() {
        // 关闭所有模态框
        const modals = ['project-modal', 'group-modal', 'cards-modal'];
        modals.forEach(modalId => {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
            }
        });
    }

    showPage(pageName) {
        // 隐藏所有页面
        document.querySelectorAll('.page').forEach(page => {
            page.classList.remove('active');
        });

        // 显示目标页面
        const targetPage = document.getElementById(`${pageName}-page`);
        if (targetPage) {
            targetPage.classList.add('active');
        }

        // 更新页面标题
        const titles = {
            dashboard: '统计仪表板',
            projects: '项目管理',
            groups: '号码组管理',
            cards: '卡密管理',
            search: '号码搜索'
        };
        document.getElementById('page-title').textContent = titles[pageName] || '管理后台';

        // 加载页面数据
        this.loadPageData(pageName);
    }

    loadPageData(pageName) {
        switch (pageName) {
            case 'dashboard':
                // 并行加载仪表板数据以提升性能
                Promise.all([
                    this.loadDashboardStats(),
                    this.loadProjectsRanking()
                ]).then(() => {
                    console.log('仪表板数据加载完成');
                }).catch(error => {
                    console.error('仪表板数据加载失败:', error);
                });
                break;
            case 'projects':
                this.loadProjects();
                break;
            case 'groups':
                if (this.currentProjectId) {
                    this.loadGroups();
                }
                break;
            case 'cards':
                if (this.currentProjectId) {
                    this.loadCards();
                }
                break;
        }
    }

    preloadDashboardData() {
        // 在后台预加载仪表板数据，提升用户体验
        console.log('开始预加载仪表板数据...');

        // 检查当前是否在仪表板页面，如果是则跳过预加载，避免重复请求
        const currentHash = window.location.hash.slice(1) || '/dashboard';
        if (currentHash === '/dashboard') {
            console.log('当前在仪表板页面，跳过预加载');
            return;
        }

        // 使用较低优先级的方式预加载
        setTimeout(() => {
            Promise.all([
                Http.get('/api/admin/dashboard/stats').catch(e => console.log('预加载统计数据失败:', e)),
                Http.get('/api/admin/projects/ranking').catch(e => console.log('预加载排行数据失败:', e))
            ]).then(() => {
                console.log('仪表板数据预加载完成');
            });
        }, 100); // 延迟100ms，让页面先渲染
    }

    onProjectChange() {
        // 更新按钮状态
        const addGroupBtn = document.getElementById('add-group-btn');
        const generateCardsBtn = document.getElementById('generate-cards-btn');
        
        if (this.currentProjectId) {
            if (addGroupBtn) addGroupBtn.disabled = false;
            if (generateCardsBtn) generateCardsBtn.disabled = false;
        } else {
            if (addGroupBtn) addGroupBtn.disabled = true;
            if (generateCardsBtn) generateCardsBtn.disabled = true;
        }

        // 重新加载当前页面数据
        const currentRoute = this.router.currentRoute;
        if (currentRoute) {
            this.loadPageData(currentRoute.replace('/', ''));
        }
    }

    async loadProjects() {
        try {
            const data = await Http.get('/api/admin/projects');
            const projectSelect = document.getElementById('global-project-list');
            
            // 更新项目选择器
            projectSelect.innerHTML = '<option value="">选择项目</option>';
            data.forEach(project => {
                const option = document.createElement('option');
                option.value = project.id;
                option.textContent = project.name;
                projectSelect.appendChild(option);
            });

            // 恢复选择状态
            if (this.currentProjectId) {
                projectSelect.value = this.currentProjectId;
                // 确保currentProject也被正确设置
                const selectedOption = projectSelect.options[projectSelect.selectedIndex];
                if (selectedOption) {
                    this.currentProject = selectedOption.text;
                }
            }

            // 更新项目管理表格
            this.updateProjectsTable(data);
        } catch (error) {
            Notification.show('加载项目失败: ' + error.message, 'error');
        }
    }

    updateProjectsTable(projects) {
        const tbody = document.getElementById('projects-table');
        if (!tbody) return;

        if (projects.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center">暂无项目</td></tr>';
            return;
        }

        tbody.innerHTML = projects.map(project => `
            <tr>
                <td>${project.id}</td>
                <td>${project.name}</td>
                <td>${project.available_cards || 0}</td>
                <td>${project.total_cards || 0}</td>
                <td>${Format.date(project.created_at, 'YYYY-MM-DD HH:mm')}</td>
                <td>
                    <button class="btn btn-sm btn-secondary" onclick="editProject(${project.id})">
                        <i class="fas fa-cog"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteProject(${project.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');


    }

    async loadDashboardStats() {
        try {
            const startTime = performance.now();
            const data = await Http.get('/api/admin/dashboard/stats');
            const endTime = performance.now();

            console.log(`仪表板统计API响应时间: ${(endTime - startTime).toFixed(2)}ms`);

            if (data.success) {
                const stats = data.stats;
                document.getElementById('today-used').textContent = Format.number(stats.today_used);
                document.getElementById('yesterday-used').textContent = Format.number(stats.yesterday_used);
                document.getElementById('total-used-cards').textContent = Format.number(stats.total_used_cards);

                // 如果是缓存数据，显示提示
                if (data.cached) {
                    console.log('仪表板统计数据来自缓存');
                }
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
            // 显示错误状态
            document.getElementById('today-used').textContent = '错误';
            document.getElementById('yesterday-used').textContent = '错误';
            document.getElementById('total-used-cards').textContent = '错误';
        }
    }

    async loadProjectsRanking() {
        const tbody = document.getElementById('ranking-table');
        if (!tbody) return;

        try {
            // 显示加载状态
            tbody.innerHTML = '<tr><td colspan="6" class="text-center">加载中...</td></tr>';

            const startTime = performance.now();
            const data = await Http.get('/api/admin/projects/ranking');
            const endTime = performance.now();

            console.log(`项目排行API响应时间: ${(endTime - startTime).toFixed(2)}ms`);

            if (data.success) {
                this.updateRankingTable(data.ranking);

                // 如果是缓存数据，显示提示
                if (data.cached) {
                    console.log('项目排行数据来自缓存');
                }
            } else {
                tbody.innerHTML = '<tr><td colspan="4" class="text-center text-danger">加载失败</td></tr>';
            }
        } catch (error) {
            console.error('加载排行数据失败:', error);
            tbody.innerHTML = '<tr><td colspan="4" class="text-center text-danger">加载失败: ' + error.message + '</td></tr>';
        }
    }

    updateRankingTable(ranking) {
        const tbody = document.getElementById('ranking-table');
        if (!tbody) return;

        if (ranking.length === 0) {
            tbody.innerHTML = '<tr><td colspan="4" class="text-center">暂无数据</td></tr>';
            return;
        }

        tbody.innerHTML = ranking.map((item, index) => `
            <tr>
                <td>${index + 1}</td>
                <td>${item.name}</td>
                <td>${Format.number(item.today_used)}</td>
                <td>${Format.number(item.used_cards)}</td>
            </tr>
        `).join('');
    }

    async loadGroups() {
        if (!this.currentProjectId) {
            document.getElementById('groups-content').innerHTML = '<p class="text-center">请先选择一个项目</p>';
            document.getElementById('global-random-control').style.display = 'none';
            return;
        }

        try {
            // 加载号码组数据
            const data = await Http.get('/api/admin/groups', { project_id: this.currentProjectId });
            this.updateGroupsTable(data);

            // 加载项目信息以获取号码组随机取号状态
            const project = await Http.get(`/api/admin/projects/${this.currentProjectId}`);
            this.updateGlobalRandomSwitch(project.is_random_global);
        } catch (error) {
            Notification.show('加载号码组失败: ' + error.message, 'error');
        }
    }

    updateGroupsTable(groups) {
        const container = document.getElementById('groups-content');
        if (!container) return;

        if (groups.length === 0) {
            container.innerHTML = '<p class="text-center">暂无号码组</p>';
            return;
        }

        const html = `
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>名称</th>
                            <th>Token</th>
                            <th>总号码</th>
                            <th>可用</th>
                            <th>已用</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${groups.map(group => `
                            <tr>
                                <td>${group.id}</td>
                                <td>${group.name}</td>
                                <td>${group.token}</td>
                                <td>${Format.number(group.total_numbers)}</td>
                                <td>${Format.number(group.available_numbers)}</td>
                                <td>${Format.number(group.used_numbers)}</td>
                                <td>
                                    <span class="badge ${group.is_disabled ? 'badge-danger' : 'badge-success'}">
                                        ${group.is_disabled ? '已禁用' : '正常'}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group-icon">
                                        <button class="btn-icon btn-icon-info" onclick="showViewNumbersModal(${group.id}, '${group.name}')" title="查看号码">
                                            <i class="fas fa-list"></i>
                                        </button>
                                        <button class="btn-icon btn-icon-primary" onclick="showAddNumbersModal(${group.id})" title="添加号码">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button class="btn-icon btn-icon-secondary" onclick="showEditGroupModal(${group.id})" title="编辑号码组">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon ${group.is_disabled ? 'btn-icon-success' : 'btn-icon-warning'}" onclick="toggleGroupStatus(${group.id}, ${group.is_disabled})" title="${group.is_disabled ? '启用号码组' : '禁用号码组'}">
                                            <i class="fas ${group.is_disabled ? 'fa-play' : 'fa-pause'}"></i>
                                        </button>
                                        <button class="btn-icon btn-icon-outline-danger" onclick="deleteGroup(${group.id})" title="删除号码组">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
        container.innerHTML = html;
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    async loadCards(page = 1) {
        try {
            const searchInput = document.getElementById('cards-search');
            const filterSelect = document.getElementById('cards-filter');
            const searchAllSwitch = document.getElementById('cards-search-all-projects-switch');

            const isUsed = filterSelect ? filterSelect.value : '';
            const keyword = searchInput ? searchInput.value.trim() : '';
            const searchAll = searchAllSwitch ? searchAllSwitch.checked : true;

            // 如果不是搜索全部项目，且没有选择项目，则显示提示
            if (!searchAll && !this.currentProjectId) {
                const container = document.getElementById('cards-content');
                if (container) {
                    container.innerHTML = '<p class="text-center">请先选择一个项目或勾选"搜索全部项目"</p>';
                }
                return;
            }

            const params = {
                page: page,
                size: 20
            };

            // 只有在不搜索全部项目时才传递project_id
            if (!searchAll && this.currentProjectId) {
                params.project_id = this.currentProjectId;
            }

            if (isUsed) params.is_used = isUsed;
            if (keyword) params.q = keyword;

            const data = await Http.get('/api/admin/cards', params);
            if (data.success) {
                this.updateCardsTable(data.cards, data.pagination);
            }
        } catch (error) {
            Notification.show('加载卡密失败: ' + error.message, 'error');
        }
    }

    updateCardsTable(cards, pagination) {
        const container = document.getElementById('cards-content');
        if (!container) return;

        if (cards.length === 0) {
            container.innerHTML = '<p class="text-center">暂无卡密数据</p>';
            return;
        }

        // 检查是否有项目名称（全项目搜索时会有）
        const hasProjectName = cards.length > 0 && cards[0].project_name !== undefined;

        const html = `
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>卡密</th>
                            ${hasProjectName ? '<th>项目</th>' : ''}
                            <th>状态</th>
                            <th>号码</th>
                            <th>验证码</th>
                            <th>创建时间</th>
                            <th>使用时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${cards.map(card => `
                            <tr>
                                <td>
                                    <code>${card.code}</code>
                                    <button class="btn btn-sm btn-icon" onclick="copyCardUrl('${card.code}')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </td>
                                ${hasProjectName ? `<td><span class="badge badge-info">${card.project_name}</span></td>` : ''}
                                <td>
                                    <span class="badge ${card.is_used ? 'badge-success' : 'badge-secondary'}">
                                        ${card.is_used ? '已使用' : '未使用'}
                                    </span>
                                </td>
                                <td>${card.number || '-'}</td>
                                <td>${card.sms_code || '-'}</td>
                                <td>${Format.date(card.create_time, 'MM-DD HH:mm')}</td>
                                <td>${Format.date(card.use_time, 'MM-DD HH:mm')}</td>
                                <td>
                                    <button class="btn btn-sm btn-danger" onclick="deleteCard('${card.code}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
            <div id="cards-pagination"></div>
        `;
        container.innerHTML = html;

        // 初始化分页
        const paginationContainer = document.getElementById('cards-pagination');
        if (this.cardsPagination) {
            this.cardsPagination.container = paginationContainer;
            this.cardsPagination.setTotal(pagination.total);
            this.cardsPagination.setCurrentPage(pagination.page);
        } else {
            this.cardsPagination = new Pagination(paginationContainer, {
                currentPage: pagination.page,
                pageSize: pagination.size,
                total: pagination.total,
                onPageChange: (page) => this.loadCards(page)
            });
        }
    }



    updateGlobalRandomSwitch(isRandomGlobal) {
        const switchElement = document.getElementById('global-random-switch');
        const controlElement = document.getElementById('global-random-control');

        if (switchElement && controlElement) {
            switchElement.checked = isRandomGlobal || false;
            controlElement.style.display = 'flex';
        }
    }

    async toggleGlobalRandom(enabled) {
        if (!this.currentProjectId) {
            Notification.show('请先选择一个项目', 'warning');
            return;
        }

        try {
            const data = { is_random_global: enabled };
            await Http.put(`/api/admin/projects/${this.currentProjectId}`, data);

            const status = enabled ? '启用' : '禁用';
            Notification.show(`号码组随机取号已${status}`, 'success');
        } catch (error) {
            // 如果更新失败，恢复开关状态
            const switchElement = document.getElementById('global-random-switch');
            if (switchElement) {
                switchElement.checked = !enabled;
            }
            Notification.show('更新失败: ' + error.message, 'error');
        }
    }
}

// 全局函数
window.showAddProjectModal = function() {
    document.getElementById('project-modal-title').textContent = '添加项目';
    document.getElementById('project-form').reset();
    document.getElementById('project-form').dataset.projectId = '';
    showModal('project-modal');
};

window.showModal = function(modalId) {
    document.getElementById(modalId).style.display = 'flex';
};

window.closeProjectModal = function() {
    document.getElementById('project-modal').style.display = 'none';
};

window.saveProject = function() {
    const form = document.getElementById('project-form');
    const projectId = form.dataset.projectId;

    const data = {
        name: document.getElementById('project-name').value.trim(),
        sms_title: document.getElementById('project-sms-title').value.trim(),
        description: document.getElementById('project-description').value.trim(),
        code_regex_patterns: document.getElementById('project-code-regex').value.trim()
    };

    if (!data.name || !data.sms_title) {
        Notification.show('请填写项目名称和短信标题', 'error');
        return;
    }

    const isEdit = projectId !== '';
    const url = isEdit ? `/api/admin/projects/${projectId}` : '/api/admin/projects';
    const method = isEdit ? 'PUT' : 'POST';

    Http.request(url, {
        method: method,
        body: JSON.stringify(data)
    })
        .then(() => {
            Notification.show(isEdit ? '项目更新成功' : '项目创建成功', 'success');
            closeProjectModal();
            app.loadProjects();
        })
        .catch(error => {
            Notification.show((isEdit ? '更新' : '创建') + '失败: ' + error.message, 'error');
        });
};



window.deleteCard = function(cardCode) {
    if (confirm('确定要删除这个卡密吗？')) {
        Http.delete(`/api/admin/cards/${cardCode}`)
            .then(() => {
                Notification.show('卡密删除成功', 'success');
                app.loadCards();
            })
            .catch(error => {
                Notification.show('删除失败: ' + error.message, 'error');
            });
    }
};

window.editProject = function(projectId) {
    // 获取项目详情
    Http.get(`/api/admin/projects/${projectId}`)
        .then(project => {
            document.getElementById('project-modal-title').textContent = '编辑项目';
            document.getElementById('project-name').value = project.name || '';
            document.getElementById('project-sms-title').value = project.sms_title || '';
            document.getElementById('project-description').value = project.description || '';
            document.getElementById('project-code-regex').value = project.code_regex_patterns || '';
            document.getElementById('project-form').dataset.projectId = projectId;
            showModal('project-modal');
        })
        .catch(error => {
            Notification.show('获取项目信息失败: ' + error.message, 'error');
        });
};

window.deleteProject = function(projectId) {
    if (confirm('确定要删除这个项目吗？这将删除所有相关数据！')) {
        Http.delete(`/api/admin/projects/${projectId}`)
            .then(() => {
                Notification.show('项目删除成功', 'success');
                app.loadProjects();
            })
            .catch(error => {
                Notification.show('删除失败: ' + error.message, 'error');
            });
    }
};

window.showAddGroupModal = function() {
    if (!app.currentProjectId) {
        Notification.show('请先选择一个项目', 'warning');
        return;
    }

    // 重置表单
    const form = document.getElementById('group-form');
    form.reset();
    form.dataset.editMode = 'false';
    form.dataset.groupId = '';

    document.getElementById('group-modal-title').textContent = '添加号码组';
    document.getElementById('group-order').value = '5';

    // 显示模态框
    document.getElementById('group-modal').style.display = 'flex';
};

window.closeGroupModal = function() {
    document.getElementById('group-modal').style.display = 'none';
};

window.saveGroup = function() {
    const form = document.getElementById('group-form');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const isEdit = form.dataset.editMode === 'true';
    const groupId = form.dataset.groupId;

    const data = {
        project_id: app.currentProjectId,
        name: document.getElementById('group-name').value,
        token: document.getElementById('group-token').value,
        order: parseInt(document.getElementById('group-order').value),
        is_random: true  // 默认启用随机取号
    };

    const request = isEdit
        ? Http.put(`/api/admin/groups/${groupId}`, data)
        : Http.post('/api/admin/groups', data);

    request
        .then(() => {
            Notification.show(`号码组${isEdit ? '更新' : '创建'}成功`, 'success');
            closeGroupModal();
            app.loadGroups();
        })
        .catch(error => {
            Notification.show(`${isEdit ? '更新' : '创建'}失败: ` + error.message, 'error');
        });
};

// 显示编辑号码组模态框
window.showEditGroupModal = async function(groupId) {
    try {
        // 获取号码组详情
        const group = await Http.get(`/api/admin/groups/${groupId}`);

        // 填充表单
        document.getElementById('group-name').value = group.name;
        document.getElementById('group-token').value = group.token;
        document.getElementById('group-order').value = group.order;

        // 设置编辑模式
        const form = document.getElementById('group-form');
        form.dataset.editMode = 'true';
        form.dataset.groupId = groupId;

        // 更新标题
        document.getElementById('group-modal-title').textContent = '编辑号码组';

        // 显示模态框
        document.getElementById('group-modal').style.display = 'flex';
    } catch (error) {
        Notification.show('获取号码组信息失败: ' + error.message, 'error');
    }
};

// 添加号码相关函数
let currentGroupId = null;
let currentNumbersData = [];
let currentNumbersPage = 1;
let numbersPerPage = 20;

window.showAddNumbersModal = function(groupId) {
    currentGroupId = groupId;

    // 重置表单
    document.getElementById('add-numbers-form').reset();
    document.getElementById('numbers-text').value = '';

    // 显示模态框
    document.getElementById('add-numbers-modal').style.display = 'flex';
};

window.closeAddNumbersModal = function() {
    document.getElementById('add-numbers-modal').style.display = 'none';
    currentGroupId = null;
};

window.saveNumbers = function() {
    if (!currentGroupId) {
        Notification.show('无效的号码组ID', 'error');
        return;
    }

    const form = document.getElementById('add-numbers-form');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const numbersText = document.getElementById('numbers-text').value.trim();
    if (!numbersText) {
        Notification.show('请输入号码数据', 'warning');
        return;
    }

    const data = {
        numbers: numbersText
    };

    Http.post(`/api/admin/groups/${currentGroupId}/numbers`, data)
        .then(response => {
            Notification.show(`成功添加 ${response.added_count} 个号码`, 'success');
            closeAddNumbersModal();
            app.loadGroups(); // 刷新号码组列表以更新统计
        })
        .catch(error => {
            Notification.show('添加号码失败: ' + error.message, 'error');
        });
};

window.toggleGroupStatus = function(groupId, isDisabled) {
    const action = isDisabled ? '启用' : '禁用';
    if (confirm(`确定要${action}这个号码组吗？`)) {
        const data = {
            is_disabled: !isDisabled
        };

        Http.put(`/api/admin/groups/${groupId}`, data)
            .then(() => {
                Notification.show(`号码组${action}成功`, 'success');
                app.loadGroups();
            })
            .catch(error => {
                Notification.show(`${action}失败: ` + error.message, 'error');
            });
    }
};

window.deleteGroup = function(groupId) {
    if (confirm('确定要删除这个号码组吗？这将删除所有相关号码！')) {
        Http.delete(`/api/admin/groups/${groupId}`)
            .then(() => {
                Notification.show('号码组删除成功', 'success');
                app.loadGroups();
            })
            .catch(error => {
                Notification.show('删除失败: ' + error.message, 'error');
            });
    }
};

// 查看号码相关函数
window.showViewNumbersModal = function(groupId, groupName) {
    currentGroupId = groupId;
    currentNumbersPage = 1;

    // 设置模态框标题
    document.getElementById('view-numbers-modal-title').textContent = `【${groupName}】号码列表`;

    // 显示模态框
    document.getElementById('view-numbers-modal').style.display = 'flex';

    // 加载号码数据
    loadNumbers();
};

window.closeViewNumbersModal = function() {
    document.getElementById('view-numbers-modal').style.display = 'none';
    currentGroupId = null;
    currentNumbersData = [];
    currentNumbersPage = 1;
};

async function loadNumbers(page = 1) {
    if (!currentGroupId) return;

    const tbody = document.getElementById('numbers-table-body');
    tbody.innerHTML = '<tr><td colspan="5" class="text-center">加载中...</td></tr>';

    try {
        const response = await Http.get(`/api/admin/groups/${currentGroupId}/numbers`, {
            page: page,
            size: numbersPerPage
        });

        if (response.success) {
            currentNumbersData = response.numbers;
            currentNumbersPage = page;
            updateNumbersTable(response.numbers);
            updateNumbersPagination(response.pagination);
        } else {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center text-danger">加载失败</td></tr>';
        }
    } catch (error) {
        console.error('加载号码失败:', error);
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-danger">加载失败: ' + error.message + '</td></tr>';
    }
}

function updateNumbersTable(numbers) {
    const tbody = document.getElementById('numbers-table-body');

    if (numbers.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center">暂无号码</td></tr>';
        return;
    }

    tbody.innerHTML = numbers.map((number, index) => {
        const statusClass = getStatusClass(number.status);
        const statusText = getStatusText(number.status, number.is_disabled);
        const lastUseTime = number.last_use_time ?
            Format.date(number.last_use_time, 'YYYY/MM/DD HH:mm:ss') : '-';

        return `
            <tr id="number-row-${number.id}" data-status="${number.status}" data-index="${index}">
                <td>${number.id}</td>
                <td>${number.number || '-'}</td>
                <td>
                    <span class="status-badge ${statusClass}">${statusText}</span>
                </td>
                <td>${lastUseTime}</td>
                <td>
                    <div class="btn-group-icon">
                        ${number.is_disabled ?
                            `<button class="btn-icon btn-icon-success" onclick="toggleNumberStatus(${number.id}, false)" title="启用号码">
                                <i class="fas fa-play"></i>
                            </button>` :
                            `<button class="btn-icon btn-icon-warning" onclick="toggleNumberStatus(${number.id}, true)" title="禁用号码">
                                <i class="fas fa-pause"></i>
                            </button>`
                        }
                        <button class="btn-icon btn-icon-outline-danger" onclick="deleteNumber(${number.id})" title="删除号码">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');

    // 如果有过滤状态，自动滚动到第一个匹配的号码
    if (window.currentFilterStatus) {
        scrollToFirstMatchingNumber(window.currentFilterStatus);
    }
}

// 滚动到第一个匹配状态的号码
function scrollToFirstMatchingNumber(targetStatus) {
    // 延迟执行，确保DOM已更新
    setTimeout(() => {
        const firstMatchingRow = document.querySelector(`tr[data-status="${targetStatus}"]`);
        if (firstMatchingRow) {
            // 滚动到该行，并添加高亮效果
            firstMatchingRow.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            // 添加临时高亮效果
            firstMatchingRow.style.backgroundColor = '#fff3cd';
            firstMatchingRow.style.transition = 'background-color 0.3s ease';

            // 2秒后移除高亮效果
            setTimeout(() => {
                firstMatchingRow.style.backgroundColor = '';
            }, 2000);
        }
    }, 100);
}

function getStatusClass(status) {
    const statusMap = {
        'available': 'status-available',
        'used': 'status-used',
        'in_use': 'status-in_use',
        'disabled': 'status-disabled'
    };
    return statusMap[status] || 'status-available';
}

function getStatusText(status, isDisabled) {
    if (isDisabled) return '已禁用';

    const statusMap = {
        'available': '可用',
        'used': '已使用',
        'in_use': '使用中'
    };
    return statusMap[status] || '可用';
}

function updateNumbersPagination(pagination) {
    const container = document.getElementById('numbers-pagination');
    if (!pagination) {
        container.innerHTML = '';
        return;
    }

    const { page: current_page, pages: total_pages, total: total_count } = pagination;

    let html = `<span class="text-muted me-3">共 ${total_count} 个号码</span>`;

    if (total_pages > 1) {
        // 上一页按钮
        html += `<button class="btn btn-sm btn-outline-secondary"
                    onclick="loadNumbers(${current_page - 1})"
                    ${current_page <= 1 ? 'disabled' : ''}>
                    <i class="fas fa-chevron-left"></i>
                </button>`;

        // 页码按钮
        const startPage = Math.max(1, current_page - 2);
        const endPage = Math.min(total_pages, current_page + 2);

        if (startPage > 1) {
            html += `<button class="btn btn-sm btn-outline-secondary" onclick="loadNumbers(1)">1</button>`;
            if (startPage > 2) {
                html += `<span class="mx-2">...</span>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            html += `<button class="btn btn-sm ${i === current_page ? 'btn-primary' : 'btn-outline-secondary'}"
                        onclick="loadNumbers(${i})">${i}</button>`;
        }

        if (endPage < total_pages) {
            if (endPage < total_pages - 1) {
                html += `<span class="mx-2">...</span>`;
            }
            html += `<button class="btn btn-sm btn-outline-secondary" onclick="loadNumbers(${total_pages})">${total_pages}</button>`;
        }

        // 下一页按钮
        html += `<button class="btn btn-sm btn-outline-secondary"
                    onclick="loadNumbers(${current_page + 1})"
                    ${current_page >= total_pages ? 'disabled' : ''}>
                    <i class="fas fa-chevron-right"></i>
                </button>`;
    }

    container.innerHTML = html;
}

// 号码操作函数
window.deleteNumber = function(numberId) {
    if (confirm('确定要删除这个号码吗？')) {
        Http.delete(`/api/admin/numbers/${numberId}`)
            .then(() => {
                Notification.show('号码删除成功', 'success');
                loadNumbers(currentNumbersPage); // 重新加载当前页
                app.loadGroups(); // 刷新号码组统计
            })
            .catch(error => {
                Notification.show('删除失败: ' + error.message, 'error');
            });
    }
};

window.toggleNumberStatus = function(numberId, disable) {
    const action = disable ? '禁用' : '启用';
    if (confirm(`确定要${action}这个号码吗？`)) {
        const data = { is_disabled: disable };

        Http.put(`/api/admin/numbers/${numberId}`, data)
            .then(() => {
                Notification.show(`号码${action}成功`, 'success');
                loadNumbers(currentNumbersPage); // 重新加载当前页
                app.loadGroups(); // 刷新号码组统计
            })
            .catch(error => {
                Notification.show(`${action}失败: ` + error.message, 'error');
            });
    }
};

// 号码过滤功能
window.filterNumbers = function(status) {
    // 将下划线转换为连字符以匹配HTML中的ID
    const buttonId = 'sort-' + status.replace('_', '-');

    // 更新按钮状态
    updateSortButtonState(buttonId);

    // 记录当前过滤状态，用于自动滚动
    window.currentFilterStatus = status;

    // 这里可以实现客户端过滤或者重新请求服务器
    // 为简化实现，我们重新请求服务器数据
    loadNumbers(1, status);
};

window.resetNumbersFilter = function() {
    // 更新按钮状态
    updateSortButtonState('sort-default');

    // 清除过滤状态
    window.currentFilterStatus = null;

    loadNumbers(1);
};

// 更新排序按钮状态
function updateSortButtonState(activeButtonId) {
    // 移除所有按钮的active状态
    const sortButtons = document.querySelectorAll('[id^="sort-"]');
    sortButtons.forEach(button => {
        button.classList.remove('active', 'btn-secondary');
        button.classList.add('btn-outline-secondary');
    });

    // 激活当前按钮
    const activeButton = document.getElementById(activeButtonId);
    if (activeButton) {
        activeButton.classList.remove('btn-outline-secondary');
        activeButton.classList.add('active', 'btn-secondary');
    }
}

// --- 号码搜索功能 ---
let currentSearchPage = 1;
let currentSearchData = [];

window.searchNumbers = async function(page = 1) {
    // 确保app对象已初始化
    if (!window.app) {
        Notification.show('系统正在初始化，请稍后再试', 'warning');
        return;
    }

    const searchAllSwitch = document.getElementById('search-all-projects-switch');
    const searchAll = searchAllSwitch ? searchAllSwitch.checked : false;

    const projectId = app.currentProjectId;
    if (!searchAll && !projectId) {
        Notification.show('请先选择项目或开启"搜索全部项目"', 'warning');
        return;
    }

    const searchInput = document.getElementById('number-search');
    const statusFilter = document.getElementById('number-status-filter');

    const query = searchInput ? searchInput.value.trim() : '';
    const status = statusFilter ? statusFilter.value : '';

    if (!query && !status) {
        document.getElementById('search-results').innerHTML = '<p class="text-center text-muted">请输入搜索条件</p>';
        document.getElementById('search-results-table').style.display = 'none';
        return;
    }

    try {
        const params = {
            page: page,
            size: 20,
            search_all: searchAll
        };

        if (!searchAll && projectId) {
            params.project_id = projectId;
        }
        if (query) params.q = query;
        if (status) params.status = status;

        const response = await Http.get('/api/admin/numbers/search', params);

        if (response.success) {
            currentSearchData = response.numbers;
            currentSearchPage = page;

            updateSearchResults(response.numbers, response.pagination, response.query, response.status_filter);
        } else {
            Notification.show(response.error || '搜索失败', 'error');
        }
    } catch (error) {
        console.error('搜索号码失败:', error);
        Notification.show('搜索失败: ' + error.message, 'error');
    }
};

function updateSearchResults(numbers, pagination, query, statusFilter) {
    const resultsDiv = document.getElementById('search-results');
    const tableDiv = document.getElementById('search-results-table');
    const tbody = document.getElementById('search-results-tbody');
    const statsSpan = document.getElementById('search-stats');

    if (numbers.length === 0) {
        resultsDiv.innerHTML = '<p class="text-center text-muted">未找到匹配的号码</p>';
        tableDiv.style.display = 'none';
        return;
    }

    // 检查是否搜索全部项目
    const searchAllSwitch = document.getElementById('search-all-projects-switch');
    const searchAll = searchAllSwitch ? searchAllSwitch.checked : false;
    const hasProjectName = numbers.some(n => n.project_name);

    // 更新表格头部
    const thead = tableDiv.querySelector('thead tr');
    if (searchAll && hasProjectName) {
        // 显示项目名称列
        thead.innerHTML = `
            <th>ID</th>
            <th>号码</th>
            <th>归属项目</th>
            <th>归属号码组</th>
            <th>状态</th>
            <th>最后使用时间</th>
            <th>操作</th>
        `;
    } else {
        // 隐藏项目名称列
        thead.innerHTML = `
            <th>ID</th>
            <th>号码</th>
            <th>归属号码组</th>
            <th>状态</th>
            <th>最后使用时间</th>
            <th>操作</th>
        `;
    }

    // 隐藏默认提示，显示表格
    resultsDiv.style.display = 'none';
    tableDiv.style.display = 'block';

    // 更新统计信息
    let statsText = `找到 ${pagination.total} 个号码`;
    if (query) statsText += `，搜索关键词："${query}"`;
    if (statusFilter) {
        const statusNames = {
            'available': '可用',
            'in_use': '使用中',
            'used': '已使用',
            'disabled': '已禁用'
        };
        statsText += `，状态：${statusNames[statusFilter] || statusFilter}`;
    }
    statsSpan.textContent = statsText;

    // 更新表格内容
    tbody.innerHTML = '';
    numbers.forEach(number => {
        const row = document.createElement('tr');

        // 状态样式
        let statusClass = 'badge ';
        switch (number.status) {
            case '可用':
                statusClass += 'bg-success';
                break;
            case '使用中':
                statusClass += 'bg-warning';
                break;
            case '已使用':
                statusClass += 'bg-secondary';
                break;
            case '已禁用':
                statusClass += 'bg-danger';
                break;
            default:
                statusClass += 'bg-light text-dark';
        }

        const projectColumn = (searchAll && hasProjectName) ?
            `<td><span class="badge badge-info">${number.project_name || '-'}</span></td>` : '';

        row.innerHTML = `
            <td>${number.id}</td>
            <td>${number.number}</td>
            ${projectColumn}
            <td>${number.group_name}</td>
            <td><span class="${statusClass}">${number.status}</span></td>
            <td>${number.last_used}</td>
            <td>
                <div class="btn-group-icon">
                    ${number.is_disabled ?
                        `<button class="btn-icon btn-icon-success" onclick="toggleSearchNumberStatus(${number.id}, false)" title="启用号码">
                            <i class="fas fa-play"></i>
                        </button>` :
                        `<button class="btn-icon btn-icon-warning" onclick="toggleSearchNumberStatus(${number.id}, true)" title="禁用号码">
                            <i class="fas fa-pause"></i>
                        </button>`
                    }
                    <button class="btn-icon btn-icon-outline-danger" onclick="deleteSearchNumber(${number.id})" title="删除号码">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;

        tbody.appendChild(row);
    });

    // 更新分页
    updateSearchPagination(pagination);
}

function updateSearchPagination(pagination) {
    const paginationDiv = document.getElementById('search-pagination');

    if (pagination.pages <= 1) {
        paginationDiv.innerHTML = '';
        return;
    }

    let paginationHtml = '<nav><ul class="pagination pagination-sm">';

    // 上一页
    if (pagination.page > 1) {
        paginationHtml += `<li class="page-item">
            <a class="page-link" href="#" onclick="searchNumbers(${pagination.page - 1})">上一页</a>
        </li>`;
    }

    // 页码
    const startPage = Math.max(1, pagination.page - 2);
    const endPage = Math.min(pagination.pages, pagination.page + 2);

    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `<li class="page-item ${i === pagination.page ? 'active' : ''}">
            <a class="page-link" href="#" onclick="searchNumbers(${i})">${i}</a>
        </li>`;
    }

    // 下一页
    if (pagination.page < pagination.pages) {
        paginationHtml += `<li class="page-item">
            <a class="page-link" href="#" onclick="searchNumbers(${pagination.page + 1})">下一页</a>
        </li>`;
    }

    paginationHtml += '</ul></nav>';
    paginationDiv.innerHTML = paginationHtml;
}

window.toggleSearchNumberStatus = function(numberId, disable) {
    const action = disable ? '禁用' : '启用';
    if (confirm(`确定要${action}这个号码吗？`)) {
        const data = { is_disabled: disable };

        Http.put(`/api/admin/numbers/${numberId}`, data)
            .then(() => {
                Notification.show(`号码${action}成功`, 'success');
                searchNumbers(currentSearchPage); // 重新搜索当前页
            })
            .catch(error => {
                Notification.show(`${action}失败: ` + error.message, 'error');
            });
    }
};

window.deleteSearchNumber = function(numberId) {
    if (confirm('确定要删除这个号码吗？删除后无法恢复！')) {
        const button = event.target.closest('button');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;

        Http.delete(`/api/admin/numbers/${numberId}`)
            .then(() => {
                Notification.show('号码删除成功', 'success');
                // 重新搜索以更新结果
                searchNumbers(currentSearchPage);
            })
            .catch(error => {
                button.innerHTML = originalText;
                button.disabled = false;
                Notification.show('删除失败: ' + error.message, 'error');
            });
    }
};

// 修改loadNumbers函数以支持状态过滤
async function loadNumbers(page = 1, status = null) {
    if (!currentGroupId) return;

    const tbody = document.getElementById('numbers-table-body');
    tbody.innerHTML = '<tr><td colspan="5" class="text-center">加载中...</td></tr>';

    try {
        const params = {
            page: page,
            size: numbersPerPage
        };

        if (status) {
            params.status = status;
        }

        const response = await Http.get(`/api/admin/groups/${currentGroupId}/numbers`, params);

        if (response.success) {
            currentNumbersData = response.numbers;
            currentNumbersPage = page;
            updateNumbersTable(response.numbers);
            updateNumbersPagination(response.pagination);
        } else {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center text-danger">加载失败</td></tr>';
        }
    } catch (error) {
        console.error('加载号码失败:', error);
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-danger">加载失败: ' + error.message + '</td></tr>';
    }
}

window.showGenerateCardsModal = function() {
    if (!app.currentProjectId) {
        Notification.show('请先选择一个项目', 'warning');
        return;
    }

    // 重置表单
    document.getElementById('cards-form').reset();
    document.getElementById('cards-count').value = '1';
    document.getElementById('cards-download').checked = false;

    // 显示模态框
    document.getElementById('cards-modal').style.display = 'flex';
};

window.closeCardsModal = function() {
    document.getElementById('cards-modal').style.display = 'none';
};

window.generateCards = function() {
    const form = document.getElementById('cards-form');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    const count = parseInt(document.getElementById('cards-count').value);
    const download = document.getElementById('cards-download').checked;

    if (count < 1 || count > 1000) {
        Notification.show('生成数量必须在1-1000之间', 'error');
        return;
    }

    // 显示加载状态
    const generateBtn = document.querySelector('#cards-modal .btn-primary');
    const originalText = generateBtn.textContent;
    generateBtn.textContent = '生成中...';
    generateBtn.disabled = true;

    const formData = new FormData();
    formData.append('project_id', app.currentProjectId);
    formData.append('count', count);
    formData.append('download', download);

    if (download) {
        // 如果需要下载，使用fetch获取数据然后创建下载链接
        const formData = new FormData();
        formData.append('project_id', app.currentProjectId);
        formData.append('count', count);
        formData.append('download', 'true');
        formData.append('csrf_token', window.csrfToken);

        fetch('/api/admin/cards', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.blob();
        })
        .then(blob => {
            // 创建下载链接
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            // 获取项目名称
            const projectName = app.currentProject || 'cards';
            // 直接使用项目名称，包括中文字符
            const safeProjectName = projectName;
            // 生成本地时间格式的文件名
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            const timestamp = `${year}${month}${day}_${hours}${minutes}${seconds}`;
            a.download = `${safeProjectName}_${timestamp}.txt`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            // 恢复按钮状态
            generateBtn.textContent = originalText;
            generateBtn.disabled = false;
            closeCardsModal();
            app.loadCards();
            Notification.show(`成功生成${count}个卡密并开始下载`, 'success');
        })
        .catch(error => {
            console.error('下载失败:', error);
            generateBtn.textContent = originalText;
            generateBtn.disabled = false;
            Notification.show('下载失败: ' + error.message, 'error');
        });
    } else {
        // 如果不需要下载，使用AJAX
        Http.post('/api/admin/cards', {
            project_id: app.currentProjectId,
            count: count,
            download: false
        })
            .then(response => {
                generateBtn.textContent = originalText;
                generateBtn.disabled = false;
                closeCardsModal();
                app.loadCards();
                Notification.show(`成功生成${response.count}个卡密`, 'success');
            })
            .catch(error => {
                generateBtn.textContent = originalText;
                generateBtn.disabled = false;
                Notification.show('生成失败: ' + error.message, 'error');
            });
    }
};



// 复制卡密URL到剪贴板
window.copyCardUrl = function(cardCode) {
    const url = `${window.location.origin}/?key=${cardCode}`;

    // 检查是否支持现代剪贴板API
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(url).then(() => {
            Notification.show('卡密链接已复制到剪贴板', 'success');
        }).catch(err => {
            console.error('复制失败:', err);
            fallbackCopy(url);
        });
    } else {
        // 使用降级方案
        fallbackCopy(url);
    }

    function fallbackCopy(text) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        try {
            document.execCommand('copy');
            Notification.show('卡密链接已复制到剪贴板', 'success');
        } catch (err) {
            console.error('降级复制也失败:', err);
            Notification.show('复制失败，请手动复制: ' + text, 'error');
        }
        document.body.removeChild(textArea);
    }
};



// 初始化应用
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new AdminApp();
    // 确保app对象在全局可用
    window.app = app;
});
