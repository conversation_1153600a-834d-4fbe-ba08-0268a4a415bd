/* 用户前台样式 - 全新白底卡片设计 */
:root {
  /* 主色调 */
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --primary-light: #dbeafe;

  /* 状态颜色 */
  --success-color: #059669;
  --success-light: #d1fae5;
  --error-color: #dc2626;
  --error-light: #fee2e2;
  --warning-color: #d97706;
  --warning-light: #fef3c7;

  /* 文字颜色 */
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;

  /* 背景颜色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-gray: #f3f4f6;

  /* 边框和阴影 */
  --border-color: #e5e7eb;
  --border-light: #f3f4f6;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* 圆角 */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;

  /* 过渡动画 */
  --transition: all 0.2s ease-in-out;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-secondary);
  min-height: 100vh;
  font-size: 16px;
}

/* 应用包装器 */
.app-wrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 容器 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 页面头部 */
.page-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: 24px 0;
}

.page-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  text-align: center;
}

.page-subtitle {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 8px 0 0 0;
  text-align: center;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  padding: 32px 0;
}

/* 内容卡片 */
.content-card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-md);
  margin-bottom: 24px;
  overflow: hidden;
  transition: var(--transition);
}

.content-card:hover {
  box-shadow: var(--shadow-lg);
}

/* 简化的卡片头部 */
.card-header-minimal {
  padding: 0;
  border: none;
  height: 0;
}

/* 更换号码按钮区域 */
.replace-button-area {
  text-align: center;
  margin-bottom: 24px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--border-color);
}

/* 卡片主体 */
.card-body {
  padding: 32px;
}

/* 错误卡片 */
.error-card {
  border-color: var(--error-color);
}

.error-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 24px;
  background: var(--error-light);
  border-radius: var(--radius-md);
}

.error-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--error-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  flex-shrink: 0;
}

.error-text {
  font-size: 0.875rem;
  color: var(--error-color);
  font-weight: 500;
}

/* 信息卡片 */
.info-card .header-icon {
  background: var(--primary-light);
  color: var(--primary-color);
}

/* 成功卡片 */
.success-card {
  border-color: var(--success-color);
}

/* 通知说明内容 */
.notice-content {
  line-height: 1.6;
}

.notice-content p {
  margin-bottom: 16px;
  color: var(--text-secondary);
}

.example-box {
  background: var(--bg-gray);
  border-radius: var(--radius-md);
  padding: 16px;
  margin-top: 16px;
}

.example-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.example-code {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
  font-size: 0.875rem;
  color: var(--primary-color);
  background: var(--bg-primary);
  padding: 8px 12px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

/* 按钮样式 */
.primary-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 16px 32px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
  min-width: 200px;
  height: 56px;
}

.primary-button:hover:not(:disabled) {
  background: var(--primary-hover);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.primary-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.action-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.action-button:hover:not(:disabled) {
  background: var(--bg-gray);
  color: var(--text-primary);
}

.action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 更换号码按钮可点击时的样式 */
.action-button:not(:disabled) {
  background: var(--warning-color);
  color: white;
  border-color: var(--warning-color);
  font-weight: 600;
}

.action-button:not(:disabled):hover {
  background: #b45309;
  border-color: #b45309;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.copy-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 8px 16px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  flex-shrink: 0;
}

.copy-button:hover:not(:disabled) {
  background: var(--primary-hover);
}

.copy-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 图标复制按钮 */
.copy-icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: var(--transition);
  flex-shrink: 0;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}

.copy-icon-button:hover:not(:disabled) {
  background: var(--primary-hover);
}

.copy-icon-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 操作区域 */
.action-area {
  text-align: center;
  padding: 24px 0;
}

/* 号码显示区域 */
.number-display-area {
  margin-bottom: 24px;
}

.number-field {
  position: relative;
  display: flex;
  align-items: center;
  padding: 16px 50px 16px 16px;
  background: var(--bg-gray);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

.field-value {
  flex: 1;
  font-family: inherit;
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-primary);
  text-align: left;
  padding: 0 4px;
}

/* 验证码显示区域 */
.code-display-area {
  margin-bottom: 24px;
}

.code-field {
  position: relative;
  display: flex;
  align-items: center;
  padding: 20px 50px 20px 24px;
  background: var(--success-light);
  border-radius: var(--radius-lg);
  border: 1px solid var(--success-color);
}

.code-value {
  flex: 1;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
  font-size: 2rem;
  font-weight: 900;
  color: var(--success-color);
  letter-spacing: 2px;
  text-align: center;
}

/* 字段标签 */
.field-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
}

/* 短信内容区域 */
.sms-content-field {
  margin-bottom: 24px;
}

.content-area {
  position: relative;
  display: flex;
}

.content-textarea {
  flex: 1;
  min-height: 120px;
  padding: 16px 50px 16px 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-primary);
  background: var(--bg-primary);
  resize: none;
  font-family: inherit;
}

.content-textarea.auto-height {
  min-height: 80px;
  height: auto;
  resize: none;
}

.content-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
}

/* 等待提示 */
.waiting-tip {
  margin-top: 12px;
  padding: 12px 16px;
  background: var(--primary-light);
  border-radius: var(--radius-md);
  border: 1px solid var(--primary-color);
}

.waiting-tip p {
  font-size: 0.875rem;
  color: var(--primary-color);
  margin: 0;
  line-height: 1.4;
  text-align: center;
}

/* 成功状态 */
.success-info {
  margin-top: 24px;
  text-align: center;
}

.success-banner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 24px;
  background: var(--success-light);
  color: var(--success-color);
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 16px;
}

.time-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* 项目描述区域 (不使用卡片框) */
.project-description {
  max-width: 800px;
  margin: 16px auto 32px auto;
  padding: 0;
}

.description-content {
  line-height: 1.6;
  color: var(--text-secondary);
  font-size: 0.875rem;
  text-align: left;
}

.description-content h1,
.description-content h2,
.description-content h3,
.description-content h4,
.description-content h5,
.description-content h6 {
  color: var(--text-primary);
  margin-bottom: 12px;
  margin-top: 20px;
}

.description-content h1:first-child,
.description-content h2:first-child,
.description-content h3:first-child,
.description-content h4:first-child,
.description-content h5:first-child,
.description-content h6:first-child {
  margin-top: 0;
}

.description-content p {
  margin-bottom: 16px;
}

.description-content ul,
.description-content ol {
  margin-bottom: 16px;
  padding-left: 24px;
}

.description-content li {
  margin-bottom: 8px;
}

.description-content a {
  color: var(--primary-color);
  text-decoration: none;
}

.description-content a:hover {
  text-decoration: underline;
}

.description-content code {
  background: var(--bg-gray);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
  font-size: 0.8rem;
}

.description-content pre {
  background: var(--bg-gray);
  padding: 16px;
  border-radius: var(--radius-md);
  overflow-x: auto;
  margin-bottom: 16px;
}

.description-content blockquote {
  border-left: 4px solid var(--primary-color);
  padding-left: 16px;
  margin: 16px 0;
  color: var(--text-secondary);
  font-style: italic;
}

/* 通知容器 */
.notification-container {
  margin-top: 24px;
  text-align: center;
}

.notification-container:empty {
  margin-top: 0;
}

/* 自定义通知样式 */
.custom-notification {
  display: inline-block;
  padding: 12px 20px;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  margin: 8px 0;
  max-width: 100%;
  word-wrap: break-word;
  animation: slideInUp 0.3s ease;
}

.custom-notification.success {
  background: var(--success-light);
  color: var(--success-color);
  border: 1px solid var(--success-color);
}

.custom-notification.error {
  background: var(--error-light);
  color: var(--error-color);
  border: 1px solid var(--error-color);
}

.custom-notification.info {
  background: var(--primary-light);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.custom-notification.warning {
  background: var(--warning-light);
  color: var(--warning-color);
  border: 1px solid var(--warning-color);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 项目信息内容 */
.info-content {
  line-height: 1.6;
  color: var(--text-secondary);
}

.info-content h1,
.info-content h2,
.info-content h3,
.info-content h4,
.info-content h5,
.info-content h6 {
  color: var(--text-primary);
  margin-bottom: 12px;
}

.info-content p {
  margin-bottom: 16px;
}

.info-content ul,
.info-content ol {
  margin-bottom: 16px;
  padding-left: 24px;
}

.info-content li {
  margin-bottom: 8px;
}

/* 响应式设计 */

/* 电脑端 - 横长竖窄的卡片设计 */
@media (min-width: 768px) {
  .container {
    max-width: 1000px;
  }

  .content-card {
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }

  .card-header {
    padding: 32px 40px;
  }

  .card-body {
    padding: 40px;
  }

  .project-description {
    margin-left: auto;
    margin-right: auto;
    padding: 0;
  }

  .number-field,
  .code-display {
    padding: 24px 32px;
  }

  .field-value {
    font-size: 1.5rem;
  }

  .code-value {
    font-size: 2.5rem;
  }

  .primary-button {
    min-width: 240px;
    height: 64px;
    font-size: 1.125rem;
    padding: 20px 40px;
  }

  .waiting-status {
    padding: 32px;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
  }
}
}

/* 手机端响应式设计 */
@media (max-width: 767px) {
  .page-header {
    padding: 16px 0;
  }

  .page-title {
    font-size: 1.5rem;
  }

  .main-content {
    padding: 20px 0;
  }

  .container {
    padding: 0 16px;
  }

  .content-card {
    margin-bottom: 16px;
    border-radius: var(--radius-lg);
  }

  .card-header-minimal {
    padding: 0;
  }

  .replace-button-area {
    margin-bottom: 20px;
    padding-bottom: 20px;
  }

  .action-button {
    width: 100%;
    justify-content: center;
  }

  .card-body {
    padding: 24px;
  }

  .number-field,
  .code-field {
    padding: 14px 40px 14px 14px;
  }

  .field-value {
    font-size: 0.9rem;
  }

  .code-value {
    font-size: 1.5rem;
  }

  .copy-icon-button {
    width: 32px;
    height: 32px;
    right: 6px;
  }

  .project-description {
    padding: 0;
  }

  .content-textarea {
    min-height: 80px;
    padding: 12px 40px 12px 12px;
  }

  .primary-button {
    width: 100%;
    min-width: auto;
    height: 52px;
  }

  .waiting-tip {
    padding: 10px 14px;
  }

  .success-banner {
    padding: 12px 16px;
    font-size: 0.8rem;
  }

  .time-info {
    font-size: 0.8rem;
  }
}

/* 超小屏幕设备优化 */
@media (max-width: 480px) {
  .page-title {
    font-size: 1.25rem;
  }

  .container {
    padding: 0 12px;
  }

  .card-header {
    padding: 16px 20px;
  }

  .header-text h2 {
    font-size: 1rem;
  }

  .header-text p {
    font-size: 0.8rem;
  }

  .card-body {
    padding: 20px;
  }

  .project-description {
    padding: 0;
  }

  .field-value {
    font-size: 0.85rem;
  }

  .code-value {
    font-size: 1.25rem;
  }

  .copy-icon-button {
    width: 28px;
    height: 28px;
    right: 4px;
  }

  .primary-button {
    height: 48px;
    font-size: 0.9rem;
  }

  .copy-button {
    padding: 6px 12px;
    font-size: 0.8rem;
  }

  .content-textarea {
    min-height: 80px;
    font-size: 0.8rem;
  }

  .waiting-status {
    padding: 16px;
  }



  .description-content {
    font-size: 0.8rem;
  }
}


}


