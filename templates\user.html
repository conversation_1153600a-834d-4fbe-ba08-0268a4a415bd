<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="theme-color" content="#ffffff">
    <title>{{ project_name or '短信接收器' }}</title>
    <!-- Font Awesome图标库 - 优先使用CDN，失败时自动切换到SVG -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css" id="cdn-fa" onload="console.log('[Icons] Font Awesome CDN loaded successfully')" onerror="console.warn('[Icons] Font Awesome CDN failed, will fallback to SVG')">
    <link rel="stylesheet" href="/static/css/user.css">
</head>
<body>
    <div class="app-wrapper">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="container">
                <h1 class="page-title">{{ project_name or '短信接收器' }}</h1>
                {% if not key %}
                <p class="page-subtitle">请使用专用链接访问</p>
                {% endif %}
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <div class="container">
                {% if not key %}
                <!-- 访问提示卡片 -->
                <div class="content-card">
                    <div class="card-header">
                        <div class="header-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="header-text">
                            <h2>访问说明</h2>
                            <p>需要使用包含卡密的专用链接</p>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="notice-content">
                            <p>本系统需要通过包含卡密的专用链接进行访问。请使用您获得的完整链接地址，或联系管理员获取正确的访问链接。</p>
                            <div class="example-box">
                                <div class="example-label">链接格式示例：</div>
                                <div class="example-code">http://域名/?key=您的卡密</div>
                            </div>
                        </div>
                    </div>
                </div>
                {% else %}
                <!-- 服务卡片 -->
                <input type="hidden" id="key-value" value="{{ key }}">

                {% if error %}
                <!-- 错误提示卡片 -->
                <div class="content-card error-card">
                    <div class="card-body">
                        <div class="error-content">
                            <div class="error-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="error-text">{{ error }}</div>
                        </div>
                    </div>
                </div>
                {% endif %}

                {% if show_get_number %}
                <!-- 获取号码卡片 -->
                <div class="content-card">
                    <div class="card-body">
                        <div class="action-area">
                            <button class="primary-button" onclick="getNumberFromServer()">
                                <i class="fas fa-mobile-alt"></i>
                                <span>获取号码</span>
                            </button>
                        </div>
                        <!-- 通知容器 -->
                        <div id="notification-container" class="notification-container"></div>
                    </div>
                </div>
                {% endif %}

                {% if number %}
                <!-- 号码和验证码统一卡片 -->
                <div class="content-card">
                    <div class="card-header-minimal">
                        <!-- 简化的头部，不显示标题 -->
                    </div>
                    <div class="card-body">
                        <!-- 更换号码按钮 -->
                        <div class="replace-button-area">
                            <button id="replace-btn" class="action-button" onclick="replaceNumber()" disabled>
                                <i class="fas fa-sync-alt"></i>
                                <span>更换号码 (<span id="countdown">{{ ui_config.REPLACE_NUMBER_COOLDOWN }}</span>s)</span>
                            </button>
                        </div>

                        <!-- 号码显示区域 -->
                        <div class="number-display-area">
                            <div class="field-label">手机号码:</div>
                            <div class="number-field">
                                <div class="field-value">{{ number }}</div>
                                <button class="copy-icon-button" onclick="copyToClipboard('{{ number }}')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>

                        {% if sms_code %}
                        <!-- 验证码显示区域 -->
                        <div class="code-display-area">
                            <div class="field-label">验证码:</div>
                            <div class="code-field">
                                <div class="code-value">{{ sms_code }}</div>
                                <button class="copy-icon-button" onclick="copyToClipboard('{{ sms_code }}')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        {% endif %}

                        {% if not sms_code %}
                        <!-- 短信内容等待区域 -->
                        <div class="sms-content-field">
                            <div class="field-label">短信内容:</div>
                            <div class="content-area">
                                <textarea class="content-textarea auto-height" readonly placeholder="正在监听短信，请耐心等待... 剩余时间: {{ ui_config.SMS_WAIT_COUNTDOWN }}秒" id="waiting-textarea"></textarea>
                                <button class="copy-icon-button" disabled>
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            <div class="waiting-tip">
                                <p>{{ ui_config.WAITING_SMS_MESSAGE }}</p>
                            </div>
                        </div>
                        {% else %}
                        <!-- 短信内容显示区域 -->
                        {% if sms_content %}
                        <div class="sms-content-field">
                            <div class="field-label">短信内容:</div>
                            <div class="content-area">
                                <textarea class="content-textarea auto-height" readonly>{{ sms_content }}</textarea>
                                <button class="copy-icon-button" onclick="copyToClipboard('{{ sms_content }}')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        {% endif %}

                        <!-- 成功提示和时间信息 -->
                        <div class="success-info">
                            <div class="success-banner">
                                <i class="fas fa-check-circle"></i>
                                <span>验证码接收成功</span>
                            </div>
                            {% if use_time %}
                            <div class="time-info">
                                <i class="fas fa-clock"></i>
                                <span>接收时间: {{ use_time }}</span>
                            </div>
                            {% endif %}
                        </div>
                        {% endif %}

                        <!-- 通知容器 -->
                        <div id="notification-container" class="notification-container"></div>
                    </div>
                </div>

                <script>
                    // 存储用户号码ID
                    {% if user_number_id %}
                    localStorage.setItem('user_number_id', '{{ user_number_id }}');
                    {% endif %}

                    // 短信等待倒计时
                    let smsCountdown = {{ ui_config.SMS_WAIT_COUNTDOWN }};
                    const waitingTextarea = document.getElementById('waiting-textarea');

                    const smsCountdownInterval = setInterval(() => {
                        smsCountdown--;
                        if (waitingTextarea) {
                            waitingTextarea.placeholder = `正在监听短信，请耐心等待... 剩余时间: ${smsCountdown}秒`;
                        }
                        if (smsCountdown <= 0) {
                            clearInterval(smsCountdownInterval);
                            if (waitingTextarea) {
                                waitingTextarea.placeholder = '等待超时，请刷新页面重试';
                            }
                            // 倒计时结束后禁用号码
                            const userNumberId = localStorage.getItem('user_number_id');
                            if (userNumberId) {
                                fetch('/api/user/timeout_number', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json'
                                    },
                                    body: JSON.stringify({
                                        user_number_id: userNumberId
                                    })
                                }).catch(error => {
                                    console.error('禁用号码失败:', error);
                                });
                            }
                        }
                    }, 1000);

                    // 自适应高度功能
                    function autoResizeTextarea() {
                        const textareas = document.querySelectorAll('.content-textarea.auto-height');
                        textareas.forEach(textarea => {
                            function resize() {
                                textarea.style.height = 'auto';
                                textarea.style.height = Math.max(80, textarea.scrollHeight) + 'px';
                            }

                            // 初始化高度
                            resize();

                            // 监听输入事件
                            textarea.addEventListener('input', resize);

                            // 监听内容变化（用于动态设置内容的情况）
                            const observer = new MutationObserver(resize);
                            observer.observe(textarea, { childList: true, subtree: true, characterData: true });
                        });
                    }

                    // 页面加载完成后初始化
                    document.addEventListener('DOMContentLoaded', autoResizeTextarea);

                    // 如果内容是动态加载的，也需要调用
                    setTimeout(autoResizeTextarea, 100);
                </script>
                {% endif %}


                {% endif %}

                {% if project_description %}
                <!-- 项目描述区域 (不使用卡片框) -->
                <div class="project-description">
                    <div class="description-content">
                        {{ project_description|safe }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 脚本文件 -->
    <script src="/static/js/utils.js"></script>
    <script>
        // 将UI配置传递给JavaScript
        {% if ui_config %}
        window.UI_CONFIG = {{ ui_config | tojson }};
        {% endif %}
    </script>
    <script src="/static/js/user.js"></script>

    <!-- 智能图标系统 - 优先CDN，失败时自动切换SVG -->
    <script src="/static/js/icons.js"></script>
</body>
</html>
