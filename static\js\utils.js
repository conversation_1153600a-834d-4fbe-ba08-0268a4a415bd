// 工具函数库

// 简单的路由管理器
class Router {
    constructor() {
        this.routes = {};
        this.currentRoute = null;
        this.init();
    }
    
    init() {
        window.addEventListener('hashchange', () => this.handleRoute());
        window.addEventListener('load', () => this.handleRoute());
    }
    
    addRoute(path, handler) {
        this.routes[path] = handler;
    }
    
    navigate(path) {
        window.location.hash = path;
    }
    
    handleRoute() {
        const hash = window.location.hash.slice(1) || '/dashboard';
        const route = this.routes[hash];
        
        if (route) {
            this.currentRoute = hash;
            route();
            this.updateActiveMenuItem(hash);
        } else {
            // 默认路由
            this.navigate('/dashboard');
        }
    }
    
    updateActiveMenuItem(path) {
        // 更新侧边栏活跃状态
        document.querySelectorAll('.sidebar-item').forEach(item => {
            item.classList.remove('active');
        });
        
        const activeItem = document.querySelector(`[data-route="${path}"]`);
        if (activeItem) {
            activeItem.classList.add('active');
        }
    }
}

// 分页组件
class Pagination {
    constructor(container, options = {}) {
        this.container = container;
        this.currentPage = options.currentPage || 1;
        this.pageSize = options.pageSize || 20;
        this.total = options.total || 0;
        this.onPageChange = options.onPageChange || (() => {});
        this.render();
    }
    
    setTotal(total) {
        this.total = total;
        this.render();
    }
    
    setCurrentPage(page) {
        this.currentPage = page;
        this.render();
    }
    
    getTotalPages() {
        return Math.ceil(this.total / this.pageSize);
    }
    
    render() {
        const totalPages = this.getTotalPages();
        if (totalPages <= 1) {
            this.container.innerHTML = '';
            return;
        }
        
        let html = '<div class="pagination">';
        
        // 上一页
        if (this.currentPage > 1) {
            html += `<button class="page-btn" data-page="${this.currentPage - 1}">‹</button>`;
        }
        
        // 页码
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);
        
        if (startPage > 1) {
            html += `<button class="page-btn" data-page="1">1</button>`;
            if (startPage > 2) {
                html += '<span class="page-ellipsis">...</span>';
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === this.currentPage ? 'active' : '';
            html += `<button class="page-btn ${activeClass}" data-page="${i}">${i}</button>`;
        }
        
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html += '<span class="page-ellipsis">...</span>';
            }
            html += `<button class="page-btn" data-page="${totalPages}">${totalPages}</button>`;
        }
        
        // 下一页
        if (this.currentPage < totalPages) {
            html += `<button class="page-btn" data-page="${this.currentPage + 1}">›</button>`;
        }
        
        html += '</div>';
        
        this.container.innerHTML = html;
        
        // 绑定事件
        this.container.querySelectorAll('.page-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const page = parseInt(e.target.dataset.page);
                if (page !== this.currentPage) {
                    this.currentPage = page;
                    this.onPageChange(page);
                    this.render();
                }
            });
        });
    }
}

// 通知组件
class Notification {
    static show(message, type = 'info', duration = null) {
        // 使用配置中的显示时间，如果没有传入duration
        if (duration === null) {
            duration = window.UI_CONFIG?.NOTIFICATION_DISPLAY_TIME || 3000;
        }

        // 查找通知容器
        const container = document.getElementById('notification-container');
        if (!container) {
            // 如果没有找到容器，使用原来的方式
            this.showOriginal(message, type, duration);
            return;
        }

        // 清除之前的通知
        container.innerHTML = '';

        const notification = document.createElement('div');
        notification.className = `custom-notification ${type}`;
        notification.textContent = message;

        container.appendChild(notification);

        // 自动关闭
        if (duration > 0) {
            setTimeout(() => {
                this.remove(notification);
            }, duration);
        }
    }

    static showOriginal(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;

        document.body.appendChild(notification);

        // 关闭按钮事件
        notification.querySelector('.notification-close').addEventListener('click', () => {
            this.remove(notification);
        });

        // 自动关闭
        if (duration > 0) {
            setTimeout(() => {
                this.remove(notification);
            }, duration);
        }

        // 显示动画
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
    }

    static remove(notification) {
        if (notification && notification.parentNode) {
            notification.style.opacity = '0';
            notification.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }
    }
}

// HTTP 请求工具
class Http {
    static async request(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': window.csrfToken || ''
            }
        };
        
        const config = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || `HTTP ${response.status}`);
            }
            
            return data;
        } catch (error) {
            console.error('HTTP Request Error:', error);
            throw error;
        }
    }
    
    static get(url, params = {}) {
        const urlParams = new URLSearchParams(params);
        const fullUrl = urlParams.toString() ? `${url}?${urlParams}` : url;
        return this.request(fullUrl);
    }
    
    static post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
    
    static put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }
    
    static delete(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    }
}

// 格式化工具
class Format {
    static date(date, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!date) return '-';
        const d = new Date(date);
        
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    }
    
    static number(num) {
        if (num === null || num === undefined) return '0';
        return num.toLocaleString();
    }
}

// 复制到剪贴板
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            Notification.show('已复制到剪贴板', 'success', 2000);
        }).catch(() => {
            fallbackCopyToClipboard(text);
        });
    } else {
        fallbackCopyToClipboard(text);
    }
}

function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        Notification.show('已复制到剪贴板', 'success', 2000);
    } catch (err) {
        Notification.show('复制失败', 'error', 2000);
    }
    
    document.body.removeChild(textArea);
}

// 导出全局变量
window.Router = Router;
window.Pagination = Pagination;
window.Notification = Notification;
window.Http = Http;
window.Format = Format;
window.copyToClipboard = copyToClipboard;
