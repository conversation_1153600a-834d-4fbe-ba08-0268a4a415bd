<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图标系统测试</title>
    <!-- Font Awesome图标库 - 优先使用CDN，失败时自动切换到SVG -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css" id="cdn-fa" onload="console.log('[Icons] Font Awesome CDN loaded successfully')" onerror="console.warn('[Icons] Font Awesome CDN failed, will fallback to SVG')">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .icon-test {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .icon-test i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-bug"></i> 图标系统测试页面</h1>
        <p>这个页面用于测试图标加载系统是否正常工作。</p>
        
        <div id="icon-status" class="status info">
            <i class="fas fa-spinner fa-spin"></i> 正在检测图标系统...
        </div>
    </div>

    <div class="test-container">
        <h2><i class="fas fa-list"></i> 图标测试</h2>
        <p>以下图标应该正常显示：</p>
        
        <div class="icon-test">
            <i class="fas fa-home"></i>
            <span>首页图标 (fa-home)</span>
        </div>
        
        <div class="icon-test">
            <i class="fas fa-user"></i>
            <span>用户图标 (fa-user)</span>
        </div>
        
        <div class="icon-test">
            <i class="fas fa-mobile-alt"></i>
            <span>手机图标 (fa-mobile-alt)</span>
        </div>
        
        <div class="icon-test">
            <i class="fas fa-key"></i>
            <span>钥匙图标 (fa-key)</span>
        </div>
        
        <div class="icon-test">
            <i class="fas fa-copy"></i>
            <span>复制图标 (fa-copy)</span>
        </div>
        
        <div class="icon-test">
            <i class="fas fa-check-circle"></i>
            <span>成功图标 (fa-check-circle)</span>
        </div>
        
        <div class="icon-test">
            <i class="fas fa-sync-alt"></i>
            <span>同步图标 (fa-sync-alt)</span>
        </div>
        
        <div class="icon-test">
            <i class="fas fa-search"></i>
            <span>搜索图标 (fa-search)</span>
        </div>
    </div>

    <div class="test-container">
        <h2><i class="fas fa-info-circle"></i> 系统信息</h2>
        <div id="system-info">
            <p>正在收集系统信息...</p>
        </div>
    </div>

    <!-- 智能图标系统 -->
    <script src="/static/js/icons.js"></script>
    
    <script>
        // 等待图标系统初始化完成
        setTimeout(() => {
            const statusDiv = document.getElementById('icon-status');
            const systemInfoDiv = document.getElementById('system-info');
            
            // 检查图标是否正常显示
            const testIcon = document.querySelector('.fas.fa-home');
            const computed = window.getComputedStyle(testIcon, ':before');
            const hasContent = computed.content && computed.content !== 'none' && computed.content !== '""';
            const hasSVG = testIcon.querySelector('svg') !== null;
            
            let iconSystem = '';
            let statusClass = '';
            let statusText = '';
            
            if (hasSVG) {
                iconSystem = 'Custom SVG (Fallback)';
                statusClass = 'warning';
                statusText = '⚠️ 使用SVG图标系统 (CDN可能不可用)';
            } else if (hasContent) {
                iconSystem = 'Font Awesome CDN';
                statusClass = 'success';
                statusText = '✅ 使用Font Awesome CDN (推荐)';
            } else {
                iconSystem = 'Unknown';
                statusClass = 'warning';
                statusText = '❌ 图标系统异常';
            }
            
            statusDiv.className = `status ${statusClass}`;
            statusDiv.innerHTML = `<i class="fas fa-info-circle"></i> ${statusText}`;
            
            // 显示系统信息
            systemInfoDiv.innerHTML = `
                <p><strong>图标系统:</strong> ${iconSystem}</p>
                <p><strong>SVG替换:</strong> ${hasSVG ? '是' : '否'}</p>
                <p><strong>Font Awesome:</strong> ${hasContent ? '可用' : '不可用'}</p>
                <p><strong>浏览器:</strong> ${navigator.userAgent}</p>
                <p><strong>时间:</strong> ${new Date().toLocaleString()}</p>
            `;
        }, 1000);
    </script>
</body>
</html>
