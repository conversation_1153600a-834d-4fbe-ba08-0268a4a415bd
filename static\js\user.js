// 用户前台脚本
class UserApp {
    constructor() {
        this.key = null;
        this.userNumberId = null;
        this.pollInterval = null;
        this.countdownInterval = null;
        this.init();
    }

    init() {
        // 获取卡密
        const keyElement = document.getElementById('key-value');
        if (keyElement) {
            this.key = keyElement.value;
        }

        // 获取用户号码ID
        this.userNumberId = localStorage.getItem('user_number_id');

        // 如果有号码但没有验证码，开始轮询
        if (this.key && this.userNumberId && !this.hasReceivedCode()) {
            this.startPolling();
            this.startCountdown();
        }

        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopPolling();
            } else if (this.key && this.userNumberId && !this.hasReceivedCode()) {
                this.startPolling();
            }
        });
    }

    hasReceivedCode() {
        // 检查页面是否已经显示了验证码
        return document.querySelector('.status-available') !== null;
    }

    async getNumberFromServer() {
        if (!this.key) {
            Notification.show('卡密无效', 'error');
            return;
        }

        const button = document.querySelector('button[onclick="getNumberFromServer()"]');
        if (button) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 获取中...';
        }

        try {
            const response = await Http.post('/api/user/get_number', { card: this.key });
            
            if (response.success) {
                this.userNumberId = response.user_number_id;
                localStorage.setItem('user_number_id', this.userNumberId);
                
                // 刷新页面显示新分配的号码
                window.location.reload();
            } else {
                Notification.show(response.error || '获取号码失败', 'error');
                if (button) {
                    button.disabled = false;
                    button.innerHTML = '<i class="fas fa-mobile-alt"></i> 获取手机号码';
                }
            }
        } catch (error) {
            Notification.show('网络错误: ' + error.message, 'error');
            if (button) {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-mobile-alt"></i> 获取手机号码';
            }
        }
    }

    startPolling() {
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
        }

        // 从全局配置获取轮询间隔，如果没有则使用默认值3000毫秒
        const pollInterval = window.UI_CONFIG?.POLL_INTERVAL_MS || 3000;

        this.pollInterval = setInterval(() => {
            this.checkForCode();
        }, pollInterval);

        // 立即检查一次
        this.checkForCode();
    }

    stopPolling() {
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
            this.pollInterval = null;
        }
    }

    async checkForCode() {
        if (!this.userNumberId) return;

        try {
            const response = await Http.get('/api/user/get_code', { 
                user_number_id: this.userNumberId 
            });

            if (response.sms_code || response.sms_content) {
                // 收到验证码，停止轮询并刷新页面
                this.stopPolling();
                this.stopCountdown();
                window.location.reload();
            } else if (response.status === 'error') {
                // 出现错误，停止轮询
                this.stopPolling();
                this.stopCountdown();
                Notification.show(response.error || '获取验证码失败', 'error');
            }
            // status === 'waiting' 时继续等待
        } catch (error) {
            console.error('检查验证码失败:', error);
            // 网络错误时不停止轮询，继续尝试
        }
    }

    startCountdown() {
        // 从全局变量获取倒计时时间，如果没有则使用默认值60秒
        let seconds = window.UI_CONFIG ? window.UI_CONFIG.REPLACE_NUMBER_COOLDOWN : 60;
        const countdownElement = document.getElementById('countdown');
        const replaceButton = document.getElementById('replace-btn');

        if (!countdownElement || !replaceButton) return;

        this.countdownInterval = setInterval(() => {
            seconds--;
            countdownElement.textContent = seconds;

            if (seconds <= 0) {
                clearInterval(this.countdownInterval);
                replaceButton.disabled = false;
                replaceButton.innerHTML = '<i class="fas fa-sync-alt"></i> 更换号码';
            }
        }, 1000);
    }

    stopCountdown() {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
    }

    async replaceNumber() {
        if (!this.key) {
            Notification.show('卡密无效', 'error');
            return;
        }

        const button = document.getElementById('replace-btn');
        if (button) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 更换中...';
        }

        try {
            const response = await Http.post('/api/user/replace_number', {
                card_code: this.key,
                user_number_id: this.userNumberId
            });

            if (response.success) {
                this.userNumberId = response.user_number_id;
                localStorage.setItem('user_number_id', this.userNumberId);
                
                // 刷新页面显示新号码
                window.location.reload();
            } else {
                Notification.show(response.error || '更换号码失败', 'error');
                if (button) {
                    button.disabled = false;
                    button.innerHTML = '<i class="fas fa-sync-alt"></i> 更换号码';
                }
            }
        } catch (error) {
            Notification.show('网络错误: ' + error.message, 'error');
            if (button) {
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-sync-alt"></i> 更换号码';
            }
        }
    }

    async disableTimeoutNumber() {
        if (!this.key || !this.userNumberId) return;

        try {
            await Http.post('/api/user/disable_timeout_number', {
                card_code: this.key,
                user_number_id: this.userNumberId
            });

            // 清除本地存储
            localStorage.removeItem('user_number_id');
            
            // 刷新页面
            window.location.reload();
        } catch (error) {
            console.error('释放号码失败:', error);
        }
    }
}

// 全局函数
window.getNumberFromServer = function() {
    if (window.userApp) {
        window.userApp.getNumberFromServer();
    }
};

window.replaceNumber = function() {
    if (window.userApp) {
        window.userApp.replaceNumber();
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.userApp = new UserApp();
    
    // 添加页面动画
    const elements = document.querySelectorAll('.fade-in');
    elements.forEach((el, index) => {
        setTimeout(() => {
            el.style.opacity = '1';
            el.style.transform = 'translateY(0)';
        }, index * 100);
    });
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (window.userApp) {
        window.userApp.stopPolling();
        window.userApp.stopCountdown();
    }
});

// 自适应高度文本域
function autoResizeTextarea() {
    const textareas = document.querySelectorAll('.form-textarea.auto-height');
    textareas.forEach(textarea => {
        // 重置高度
        textarea.style.height = 'auto';
        // 设置为内容高度
        textarea.style.height = Math.max(40, textarea.scrollHeight) + 'px';

        // 监听输入事件
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.max(40, this.scrollHeight) + 'px';
        });
    });
}

// 页面加载完成后初始化自适应高度
document.addEventListener('DOMContentLoaded', autoResizeTextarea);
