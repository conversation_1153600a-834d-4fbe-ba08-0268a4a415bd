/* 管理后台样式 */
:root {
  --primary-color: #0d6efd;
  --secondary-color: #6c757d;
  --success-color: #198754;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #0dcaf0;
  --light-color: #f8f9fa;
  --dark-color: #212529;
  --gray-color: #6c757d;
  --border-radius: 0.5rem;
  --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --sidebar-width: 260px;
  --header-height: 60px;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f5f7fa;
  overflow-x: hidden;
}

/* 布局 */
.admin-layout {
  display: flex;
  min-height: 100vh;
}

/* 侧边栏 */
.sidebar {
  width: var(--sidebar-width);
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  color: white;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 1000;
  overflow-y: auto;
  transition: transform 0.3s ease;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.sidebar-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}

.project-selector {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.project-selector select {
  width: 100%;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 0.9rem;
}

.project-selector select option {
  background: #2c3e50;
  color: white;
}

.sidebar-menu {
  padding: 10px 0;
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.sidebar-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-left-color: var(--primary-color);
}

.sidebar-item.active {
  background: rgba(13, 110, 253, 0.2);
  border-left-color: var(--primary-color);
}

.sidebar-item i {
  font-size: 1.1rem;
  margin-right: 12px;
  width: 20px;
  text-align: center;
  vertical-align: middle;
  line-height: 1;
  display: inline-block;
}

.sidebar-item span {
  font-size: 0.95rem;
  font-weight: 500;
}

.sidebar-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-footer a {
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  padding: 8px 0;
  transition: color 0.3s ease;
}

.sidebar-footer a:hover {
  color: white;
}

.sidebar-footer i {
  margin-right: 8px;
}

/* 主内容区 */
.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  min-height: 100vh;
  background: #f5f7fa;
}

.content-header {
  background: white;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.content-header h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.content-body {
  padding: 30px;
}

/* 页面容器 */
.page {
  display: none;
  animation: fadeIn 0.3s ease;
}

.page.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 卡片 */
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 24px;
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.card-header {
  background: #f8f9fa;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.card-body {
  padding: 24px;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.stat-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.stat-card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 1.5rem;
  color: white;
}

.stat-card-icon i {
  vertical-align: middle;
  line-height: 1;
}

.stat-card-icon.primary { background: var(--primary-color); }
.stat-card-icon.success { background: var(--success-color); }
.stat-card-icon.warning { background: var(--warning-color); }
.stat-card-icon.info { background: var(--info-color); }

.stat-card-title {
  font-size: 0.9rem;
  color: #6c757d;
  margin: 0;
}

.stat-card-value {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0;
}

.stat-card-change {
  font-size: 0.85rem;
  margin-top: 8px;
}

.stat-card-change.positive {
  color: var(--success-color);
}

.stat-card-change.negative {
  color: var(--danger-color);
}

/* 表格 */
.table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle; /* 垂直居中对齐 */
}

/* 操作列特殊处理 */
.table td:last-child {
  vertical-align: middle;
  min-height: 60px; /* 确保有足够高度容纳多行按钮 */
}

/* 表格文本换行处理 */
.table td {
  word-wrap: break-word;
  word-break: break-all;
  max-width: 200px; /* 限制单元格最大宽度 */
}

/* 名称列特殊处理 */
.table td:nth-child(2) {
  max-width: 150px;
  white-space: normal; /* 允许换行 */
  line-height: 1.4;
}

.table th {
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table tbody tr:hover {
  background: #f8f9fa;
}

/* 按钮 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0b5ed7;
  transform: translateY(-1px);
}

.btn-success {
  background: var(--success-color);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #157347;
  transform: translateY(-1px);
}

.btn-danger {
  background: var(--danger-color);
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #bb2d3b;
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--secondary-color);
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #5c636a;
  transform: translateY(-1px);
}

.btn-sm {
  padding: 6px 12px;
  font-size: 0.8rem;
}

.btn-icon {
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 50%;
}

/* 表单 */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.form-control {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 0.95rem;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.1);
}

.form-text {
  font-size: 0.85rem;
  color: #6c757d;
  margin-top: 4px;
}

/* 分页 */
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 20px;
}

.page-btn {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  background: white;
  color: #495057;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.page-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.page-btn.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.page-ellipsis {
  padding: 8px 4px;
  color: #6c757d;
}

/* 通知 */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  min-width: 300px;
  max-width: 500px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.notification.show {
  transform: translateX(0);
}

.notification-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  color: white;
}

.notification-info {
  background: var(--info-color);
}

.notification-success {
  background: var(--success-color);
}

.notification-warning {
  background: var(--warning-color);
  color: #212529;
}

.notification-error {
  background: var(--danger-color);
}

.notification-close {
  background: none;
  border: none;
  color: inherit;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0;
  margin-left: 12px;
}

/* 徽章 */
.badge {
  display: inline-block;
  padding: 4px 8px;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 4px;
  text-align: center;
  white-space: nowrap;
}

.badge-primary {
  background: var(--primary-color);
  color: white;
}

.badge-success {
  background: var(--success-color);
  color: white;
}

.badge-danger {
  background: var(--danger-color);
  color: white;
}

.badge-warning {
  background: var(--warning-color);
  color: #212529;
}

.badge-secondary {
  background: var(--secondary-color);
  color: white;
}

/* 全局图标样式修复 */
.fa, .fas, .far, .fab, .fal, .fad, .fat, .fass {
  vertical-align: -0.125em;
  line-height: 1;
}

/* 特定容器中的图标对齐 */
.sidebar-header i,
.card-header i,
.modal-header i,
.notification-content i {
  vertical-align: middle;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.mb-3 {
  margin-bottom: 1rem;
}

.mt-3 {
  margin-top: 1rem;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
}

.col-md-3 {
  flex: 0 0 25%;
  max-width: 25%;
  padding: 0 15px;
}

.col-md-4 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
  padding: 0 15px;
}

.col-md-6 {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 0 15px;
}

.col-md-8 {
  flex: 0 0 66.666667%;
  max-width: 66.666667%;
  padding: 0 15px;
}

/* 模态框 */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.5);
}

.modal.show {
  display: flex;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9998;
  display: none;
}

.modal-overlay.show {
  display: block;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease;
}

.modal-content.modal-large {
  max-width: 800px;
  width: 95%;
}

/* 固定表头 */
.table-header-sticky {
  position: sticky;
  top: 0;
  background: white;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 分页容器 */
.pagination-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.pagination-container .btn {
  min-width: 40px;
}

/* 状态徽章样式 */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-available {
  background: #d4edda;
  color: #155724;
}

.status-used {
  background: #f8d7da;
  color: #721c24;
}

.status-in_use {
  background: #fff3cd;
  color: #856404;
}

.status-disabled {
  background: #e2e3e5;
  color: #6c757d;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: #f8f9fa;
  color: #495057;
}

.modal-body {
  padding: 24px;
}

.modal-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding: 20px 24px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
}

/* 响应式 */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .content-body {
    padding: 20px;
  }

  .row {
    margin: 0;
  }

  .col-md-3,
  .col-md-4,
  .col-md-6,
  .col-md-8 {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0;
    margin-bottom: 10px;
  }
}

/* 美化的图标按钮样式 */
.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  padding: 0;
  border-radius: 8px;
  border: 1px solid transparent;
  font-size: 14px;
  line-height: 1;
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.btn-icon:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-icon:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-icon i {
  font-size: 14px;
  vertical-align: middle;
  line-height: 1;
}

/* 通用按钮图标样式 */
.btn i {
  vertical-align: middle;
  line-height: 1;
  margin-right: 6px;
}

.btn i:last-child {
  margin-right: 0;
  margin-left: 6px;
}

.btn i:only-child {
  margin: 0;
}

/* 不同类型的图标按钮 */
.btn-icon-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: #667eea;
}

.btn-icon-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  color: white;
}

.btn-icon-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
  border-color: #6c757d;
}

.btn-icon-secondary:hover {
  background: linear-gradient(135deg, #5a6268 0%, #3d4449 100%);
  color: white;
}

.btn-icon-success {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
  color: white;
  border-color: #11998e;
}

.btn-icon-success:hover {
  background: linear-gradient(135deg, #0e8678 0%, #32d96a 100%);
  color: white;
}

.btn-icon-danger {
  background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
  color: white;
  border-color: #ff416c;
}

.btn-icon-danger:hover {
  background: linear-gradient(135deg, #e6395f 0%, #e6421f 100%);
  color: white;
}

.btn-icon-warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  border-color: #f093fb;
}

.btn-icon-warning:hover {
  background: linear-gradient(135deg, #ed7fe8 0%, #f24458 100%);
  color: white;
}

.btn-icon-info {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border-color: #4facfe;
}

.btn-icon-info:hover {
  background: linear-gradient(135deg, #3d8bfe 0%, #00d9fe 100%);
  color: white;
}

.btn-icon-outline {
  background: white;
  border-width: 1px;
  border-style: solid;
}

.btn-icon-outline-primary {
  color: #667eea;
  border-color: #667eea;
}

.btn-icon-outline-primary:hover {
  background: #667eea;
  color: white;
}

.btn-icon-outline-success {
  color: #11998e;
  border-color: #11998e;
}

.btn-icon-outline-success:hover {
  background: #11998e;
  color: white;
}

.btn-icon-outline-danger {
  color: #ff416c;
  border-color: #ff416c;
}

.btn-icon-outline-danger:hover {
  background: #ff416c;
  color: white;
}

.btn-icon-outline-warning {
  color: #f093fb;
  border-color: #f093fb;
}

.btn-icon-outline-warning:hover {
  background: #f093fb;
  color: white;
}

.btn-icon-outline-info {
  color: #4facfe;
  border-color: #4facfe;
}

.btn-icon-outline-info:hover {
  background: #4facfe;
  color: white;
}

/* 按钮组间距 */
.btn-group-icon {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
  justify-content: center;
  max-width: 120px; /* 限制最大宽度，超出后换行 */
}

.btn-group-icon .btn-icon {
  margin: 0;
  flex-shrink: 0; /* 防止按钮被压缩 */
}

/* 加载状态 */
.btn-icon[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.btn-icon[disabled]:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* SVG图标显示优化 */
.fa, .fas, .far, .fab {
  opacity: 1 !important;
  transition: opacity 0.3s ease;
}

/* 确保图标在所有浏览器中正确显示 */
.fa, .fas, .far, .fab, .fal, .fad, .fat, .fass {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* 号码组随机取号开关样式 */
.header-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.global-random-control {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 16px;
  background: rgba(13, 110, 253, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(13, 110, 253, 0.2);
}

.switch-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--primary-color);
  margin: 0;
  white-space: nowrap;
}

/* iOS风格开关 */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  margin: 0;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s ease;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s ease;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
  background-color: var(--success-color);
}

input:focus + .slider {
  box-shadow: 0 0 0 2px rgba(25, 135, 84, 0.25);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* 开关状态指示 */
.slider:after {
  content: 'OFF';
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 10px;
  font-weight: bold;
  color: #666;
  transition: 0.3s ease;
}

input:checked + .slider:after {
  content: 'ON';
  left: 6px;
  right: auto;
  color: white;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .header-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .global-random-control {
    justify-content: space-between;
  }
}
