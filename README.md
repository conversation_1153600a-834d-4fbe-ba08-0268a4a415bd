# SMS验证码系统 V2

这是一个全新设计的SMS验证码接收系统，具有现代化的界面和强大的功能。

## 🚀 新特性

### 📊 统计仪表板
- **项目使用排行榜** - 根据使用量对项目进行排序
- **每日使用统计** - 显示今日和历史使用数据
- **实时数据监控** - 总项目数、卡密数、可用号码等关键指标
- **使用趋势图表** - 可视化展示使用趋势（预留Chart.js集成）

### 🎯 改进的管理功能
- **单页面应用** - 流畅的页面切换，无需刷新
- **合并卡密管理** - 生成和管理功能整合到一个页面
- **分页加载** - 防止大量数据导致页面卡顿
- **智能搜索** - 支持按卡密、号码、验证码搜索
- **批量操作** - 支持批量生成卡密并下载CSV

### 🎨 现代化界面
- **响应式设计** - 完美适配桌面和移动设备
- **图标化按钮** - 使用Bootstrap Icons提升用户体验
- **流畅动画** - 页面切换和交互动画
- **深色侧边栏** - 现代化的管理后台设计
- **卡片式布局** - 清晰的信息层次结构

### 🔧 系统优化
- **国内CDN** - 使用bootcdn.net提供的CDN服务
- **项目描述换行** - 正确处理Markdown中的换行符
- **移除版权信息** - 清洁的用户界面
- **系统设置** - 移至侧边栏底部，更符合用户习惯

## 📁 项目结构

```
sms-system-v2/
├── app.py                 # 主应用文件
├── models.py             # 数据模型
├── requirements.txt      # 依赖包
├── static/              # 静态文件
│   ├── css/
│   │   ├── admin.css    # 管理后台样式
│   │   └── user.css     # 用户前台样式
│   └── js/
│       ├── admin.js     # 管理后台脚本
│       ├── user.js      # 用户前台脚本
│       └── utils.js     # 工具函数
├── templates/           # 模板文件
│   ├── admin.html       # 管理后台
│   ├── user.html        # 用户前台
│   └── login.html       # 登录页面
└── instance/           # 数据库文件目录
```

## 🛠️ 安装和运行

### 1. 安装依赖
```bash
cd sms-system-v2
pip install -r requirements.txt
```

### 2. 运行应用
```bash
python app.py
```

### 3. 访问系统
- **用户前台**: http://localhost:5600/sms?key=卡密
- **管理后台**: http://localhost:5600/admin
- **默认账号**: admin / admin123

## 📊 API接口

### 统计API
- `GET /api/admin/dashboard/stats` - 获取仪表板统计数据
- `GET /api/admin/projects/ranking` - 获取项目使用排行

### 项目管理API
- `GET /api/admin/projects` - 获取项目列表
- `POST /api/admin/projects` - 创建新项目
- `PUT /api/admin/projects/{id}` - 更新项目
- `DELETE /api/admin/projects/{id}` - 删除项目

### 卡密管理API（改进）
- `GET /api/admin/cards?page=1&size=20` - 分页获取卡密
- `POST /api/admin/cards` - 生成卡密（支持下载）
- `DELETE /api/admin/cards/{code}` - 删除卡密

### 号码组管理API
- `GET /api/admin/groups?project_id=1` - 获取号码组
- `POST /api/admin/groups` - 创建号码组
- `GET /api/admin/groups/{id}/numbers` - 获取号码列表（分页）

### 系统设置API
- `GET /api/admin/settings` - 获取系统设置
- `POST /api/admin/settings` - 保存系统设置

## 🎯 主要改进

### 1. 侧边栏统计功能
- 新增统计仪表板页面
- 项目使用量排行榜
- 每日使用数据统计
- 实时数据更新

### 2. 单页面应用架构
- 使用路由管理页面切换
- 流畅的页面转换动画
- 无刷新数据更新
- 状态保持和恢复

### 3. 卡密管理合并
- 生成和管理功能整合
- 分页加载防止卡顿
- 只在生成时提供下载
- 智能搜索和过滤

### 4. 界面优化
- 使用图标代替部分文字
- 移除刷新按钮，自动更新
- 系统设置移至底部
- 修复项目描述换行
- 移除版权信息

### 5. 技术优化
- 使用国内CDN加速
- 响应式设计
- 现代化CSS Grid/Flexbox布局
- 组件化JavaScript架构

## 🔄 数据迁移

如果从旧版本升级，数据库会自动创建新的统计表。现有数据保持兼容。

## 📝 开发说明

### 前端架构
- **Router类**: 管理单页面应用路由
- **Pagination类**: 通用分页组件
- **Http类**: 统一的API请求处理
- **Notification类**: 消息通知组件

### 后端架构
- **Flask**: Web框架
- **SQLAlchemy**: ORM数据库操作
- **统计模块**: 每日数据统计和排行
- **分页支持**: API级别的分页实现

## 🎨 界面预览

### 管理后台
- 深色侧边栏配现代化图标
- 统计卡片展示关键数据
- 表格式数据展示
- 响应式布局适配移动端

### 用户前台
- 渐变背景设计
- 卡片式信息展示
- 实时状态更新
- 优雅的加载动画

## 📞 技术支持

如有问题，请检查：
1. Python版本 >= 3.7
2. 依赖包是否正确安装
3. 端口5600是否被占用
4. 数据库文件权限是否正确
